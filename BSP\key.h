/*
 * key.h
 * 按键模块化处理头文件
 * 按键USER_SWITCH_2 PB21
 */

#ifndef KEY_H_
#define KEY_H_

#include <stdint.h>
#include <stdbool.h>
#include "ti_msp_dl_config.h"

// 按键ID定义
#define KEY_ID_USER        0   // 用户按键ID
#define KEY_ID_MENU        1   // 菜单按键ID
#define KEY_ID_UP          2   // 上按键ID
#define KEY_ID_DOWN        3   // 下按键ID
#define KEY_ID_OK          4   // 确认按键ID
// 可根据实际需求添加更多按键ID

// 按键事件定义 (按键ID左移8位 | 按键状态)
#define KEY_MAKE_EVENT(id, state)   (((id) << 8) | (state))

// 按键状态枚举
typedef enum {
    KEY_RELEASED = 0,     // 按键释放状态
    KEY_PRESSED,          // 按键按下状态
    KEY_LONG_PRESSED      // 按键长按状态
} KeyState_t;

// 按键事件宏定义
#define KEY_EVENT_NONE            0x0000      // 无按键事件

// 用户按键事件
#define KEY_EVENT_USER_RELEASE    KEY_MAKE_EVENT(KEY_ID_USER, KEY_RELEASED)    // 用户按键释放
#define KEY_EVENT_USER_PRESS      KEY_MAKE_EVENT(KEY_ID_USER, KEY_PRESSED)     // 用户按键短按
#define KEY_EVENT_USER_LONGPRESS  KEY_MAKE_EVENT(KEY_ID_USER, KEY_LONG_PRESSED)// 用户按键长按

// 菜单按键事件
#define KEY_EVENT_MENU_RELEASE    KEY_MAKE_EVENT(KEY_ID_MENU, KEY_RELEASED)    // 菜单按键释放
#define KEY_EVENT_MENU_PRESS      KEY_MAKE_EVENT(KEY_ID_MENU, KEY_PRESSED)     // 菜单按键短按
#define KEY_EVENT_MENU_LONGPRESS  KEY_MAKE_EVENT(KEY_ID_MENU, KEY_LONG_PRESSED)// 菜单按键长按

// 上按键事件
#define KEY_EVENT_UP_RELEASE      KEY_MAKE_EVENT(KEY_ID_UP, KEY_RELEASED)      // 上按键释放
#define KEY_EVENT_UP_PRESS        KEY_MAKE_EVENT(KEY_ID_UP, KEY_PRESSED)       // 上按键短按
#define KEY_EVENT_UP_LONGPRESS    KEY_MAKE_EVENT(KEY_ID_UP, KEY_LONG_PRESSED)  // 上按键长按

// 下按键事件
#define KEY_EVENT_DOWN_RELEASE    KEY_MAKE_EVENT(KEY_ID_DOWN, KEY_RELEASED)    // 下按键释放
#define KEY_EVENT_DOWN_PRESS      KEY_MAKE_EVENT(KEY_ID_DOWN, KEY_PRESSED)     // 下按键短按
#define KEY_EVENT_DOWN_LONGPRESS  KEY_MAKE_EVENT(KEY_ID_DOWN, KEY_LONG_PRESSED)// 下按键长按

// 确认按键事件
#define KEY_EVENT_OK_RELEASE      KEY_MAKE_EVENT(KEY_ID_OK, KEY_RELEASED)      // 确认按键释放
#define KEY_EVENT_OK_PRESS        KEY_MAKE_EVENT(KEY_ID_OK, KEY_PRESSED)       // 确认按键短按
#define KEY_EVENT_OK_LONGPRESS    KEY_MAKE_EVENT(KEY_ID_OK, KEY_LONG_PRESSED)  // 确认按键长按

// 按键结构体定义
typedef struct {
    GPIO_Regs *port;      // 按键所在的GPIO端口
    uint32_t pin;             // 按键对应的引脚
    bool activeHigh;          // 按键激活电平，true=高电平有效，false=低电平有效
    uint32_t longPressTime;   // 长按时间阈值（毫秒）
    uint32_t debounceTime;    // 去抖时间（毫秒）
    KeyState_t state;         // 当前按键状态
    uint32_t pressTime;       // 按键按下时间计数
    bool prevLevel;           // 上一次按键电平
    uint32_t debounceCounter; // 去抖计数器
    bool stablePressed;       // 稳定按下状态（去抖后）
    uint8_t id;               // 按键ID，用于区分不同按键
} Key_t;

/**
 * @brief 初始化按键
 * 
 * @param key 按键结构体指针
 * @param port 按键所在的GPIO端口
 * @param pin 按键对应的引脚
 * @param activeHigh 按键激活电平，true=高电平有效，false=低电平有效
 * @param longPressTime 长按时间阈值（毫秒）
 * @param debounceTime 去抖时间（毫秒）
 * @param id 按键ID，用于区分不同按键
 */
void KEY_Init(Key_t *key, GPIO_Regs *port, uint32_t pin, 
              bool activeHigh, uint32_t longPressTime, uint32_t debounceTime, uint8_t id);

/**
 * @brief 初始化所有按键
 * 
 * @param keys 按键结构体数组指针
 */
void KEY_All_Init(Key_t *keys[]);

/**
 * @brief 扫描按键状态
 * 
 * @param key 按键结构体指针
 * @param timeIncMs 时间增量（毫秒），用于计算长按时间
 */
void KEY_Scan(Key_t *key, uint32_t timeIncMs);

/**
 * @brief 获取按键状态
 * 
 * @param key 按键结构体指针
 * @return KeyState_t 按键状态
 */
KeyState_t KEY_GetState(Key_t *key);

/**
 * @brief 获取按键事件
 * 
 * @param key 按键结构体指针
 * @return uint16_t 按键事件码（按键ID和状态的组合）
 */
uint16_t KEY_GetEvent(Key_t *key);

/**
 * @brief 同时扫描多个按键状态
 * 
 * @param keys 按键结构体指针数组
 * @param numKeys 按键数量
 * @param timeIncMs 时间增量（毫秒）
 */
void KEY_ScanMultiple(Key_t *keys[], uint8_t numKeys, uint32_t timeIncMs);

/**
 * @brief 检查指定按键是否被按下（包括短按和长按）
 * 
 * @param key 按键结构体指针
 * @return true 按键被按下
 * @return false 按键未被按下
 */
bool KEY_IsPressed(Key_t *key);

/**
 * @brief 检查指定按键是否被长按
 * 
 * @param key 按键结构体指针
 * @return true 按键被长按
 * @return false 按键未被长按
 */
bool KEY_IsLongPressed(Key_t *key);

#endif /* KEY_H_ */ 