/*
本项目是基于陶晶驰7寸串口屏与mspm0g3507开发板的触屏交互系统。
由交互端（7寸串口屏）产生控制指令，通过uart发送至mcu，
mcu再将必要数据如adc测量结果发送至交互端，
实现各种功能与数据可视化的功能。

注意！！！
状态机标志位只会保留命令里的page_id,其余部分在执行后会被清除。
需要在特定界面持续执行的任务，请放置在StateMachine_HandleDataSending()部分。

 */
#include "ti_msp_dl_config.h"
#include "key.h"
#include "motor.h"
#include "delay.h"
#include "adc.h"
#include "usart.h"
#include "time.h"
#include "tjc.h"
#include "send_info.h"
#include "state_machine.h"
#include "oscilloscope.h"
#include "DSP/dsp_fft_test.h"
#include "DSP/dsp_spectrum_test.h"
#include <stdio.h>
#include <string.h>

// 全局变量
Key_t userKey; // 全局按键对象
Key_t userKey1; // 全局按键对象 未实现
Key_t *allKeys[] = {&userKey,&userKey1}; // 所有按键的指针数组

uint16_t prevKeyEvent = KEY_EVENT_NONE; // 用于跟踪上一次按键事件
int adc_value = 0; // ADC转换结果
extern int motor_speed; // 当前电机转速百分比
extern int osc_dma_interrupt_count;
char uart_buffer[1000]; // UART发送缓冲区


// 非阻塞时间管理变量
static uint32_t last_key_scan_time = 0;
static uint32_t last_debug_time = 0;
static uint32_t led_blink_time = 0;
static uint32_t last_adc_sample_time = 0;
static uint32_t last_adc_send_time = 0;
static uint32_t last_osc_measure_time = 0;
static bool led_blink_active = false;

// 按键控制标志位
volatile bool key_motor_speed_increase = false;
volatile bool key_motor_stop = false;
volatile bool key_encoder_reset = false;

#define KEY_SCAN_INTERVAL 10    // 按键扫描间隔 10ms
#define DEBUG_INTERVAL 5000     // 调试信息间隔 5秒
#define LED_BLINK_INTERVAL 100  // LED闪烁间隔 100ms
#define ADC_SAMPLE_INTERVAL 50  // ADC采样间隔 50ms
#define ADC_SEND_INTERVAL 1000  // ADC值发送间隔 1秒
#define OSC_MEASURE_INTERVAL 2000  // 示波器测量间隔 2秒

// 函数声明
void KET_Event(void); // 按键事件处理（保留兼容性）
void KET_Event_NonBlocking(void); // 非阻塞按键事件处理
void Handle_LED_Blink_NonBlocking(void); // 非阻塞LED闪烁处理
void Handle_ADC_Sampling_NonBlocking(void); // 非阻塞ADC采样处理
void TJC_ProcessReceivedData(void); // TJC协议处理函数
void TJC_FrameReceivedCallback(const TJC_Frame* frame);

/*-------------------------------------------------------------------------------------------------------------------------------------------------------------*/

int main(void)
{  
    SYSCFG_DL_init(); // 初始化系统配置
    UART_Init(NULL); // uart0初始化
    delay_init();  // 初始化延时模块，确保get_tick_ms可用
    
    DL_GPIO_setPins(USER_LED_1_PORT, USER_LED_1_PIN_A_0_PIN);// 初始化LED引脚（默认熄灭）
    KEY_All_Init(allKeys); // 使用KEY_All_Init函数初始化所有按键
    ADC_Init(); // 初始化ADC转换器
    timer_init(); // 初始化编码器定时器
    
    // 初始化TJC解析器
    TJC_ParserInit(&tjc_parser);

    // 初始化状态机
    StateMachine_Init();

    // 发送初始化完成消息
    UART_SendString("系统初始化完成！\r\n");

    // DSP FFT模块测试（可选，用于验证环境搭建）
    #ifdef DSP_FFT_TEST_ENABLE
    UART_SendString("开始DSP FFT模块测试...\r\n");
    DSP_Status dsp_fft_test_result = DSP_FFT_RunAllTests();
    if (dsp_fft_test_result == DSP_STATUS_OK) {
        UART_SendString("DSP FFT模块测试通过！\r\n");
    } else {
        UART_SendString("DSP FFT模块测试失败！\r\n");
    }
    #endif

    // DSP频谱分析模块测试（可选）
    #ifdef DSP_SPECTRUM_TEST_ENABLE
    UART_SendString("开始DSP频谱分析模块测试...\r\n");
    DSP_Status dsp_spectrum_test_result = DSP_Spectrum_RunAllTests();
    if (dsp_spectrum_test_result == DSP_STATUS_OK) {
        UART_SendString("DSP频谱分析模块测试通过！\r\n");
    } else {
        UART_SendString("DSP频谱分析模块测试失败！\r\n");
    }
    #endif

    // 初始化时间基准
    last_key_scan_time = get_tick_ms();
    last_debug_time = get_tick_ms();

    while (1) {
        uint32_t current_time = get_tick_ms();

        // 非阻塞按键扫描 - 每10ms扫描一次
        if (current_time - last_key_scan_time >= KEY_SCAN_INTERVAL) {
            KET_Event_NonBlocking();
            last_key_scan_time = current_time;
        }

        // TJC命令状态机处理 - 实时处理
        StateMachine_Process();

        // 处理串口屏发来的数据 - 实时处理
        TJC_ProcessReceivedData();

        // 非阻塞LED闪烁处理
        Handle_LED_Blink_NonBlocking();

        // 非阻塞ADC采样处理
        Handle_ADC_Sampling_NonBlocking();

        // // 每5秒发送一次调试统计信息
        // if (current_time - last_debug_time >= DEBUG_INTERVAL) {
        //     UART_GetDebugStats();
        //     last_debug_time = current_time;
        // }

    }
}

// TJC协议回调函数 - 转发到状态机
void TJC_FrameReceivedCallback(const TJC_Frame* frame)
{
    // 直接调用状态机的回调函数
    StateMachine_FrameReceivedCallback(frame);
}

/**
 * @brief 处理接收到的TJC协议数据
 */
void TJC_ProcessReceivedData(void) {
    // 使用新的UART_ProcessTJCData函数处理DMA接收到的数据
    UART_ProcessTJCData();
}

/**
 * @brief 非阻塞按键事件处理
 */
void KET_Event_NonBlocking(void) {
    // 保存之前的按键事件
    prevKeyEvent = KEY_GetEvent(&userKey);
    uint16_t prevKeyEvent1 = KEY_GetEvent(&userKey1);

    // 非阻塞按键扫描 - 使用实际的扫描间隔
    KEY_ScanMultiple(allKeys, 2, KEY_SCAN_INTERVAL);

    // 获取当前按键事件
    uint16_t currentKeyEvent = KEY_GetEvent(&userKey);
    uint16_t currentKeyEvent1 = KEY_GetEvent(&userKey1);

    // ADC采样由独立的非阻塞函数处理，这里不需要获取ADC值

    // 根据按键事件处理 - 只设置标志位，不直接控制硬件
    switch (currentKeyEvent) {
        case KEY_EVENT_USER_PRESS:
            // 用户按键短按 - 设置增加电机转速标志位
            if (prevKeyEvent == KEY_EVENT_USER_RELEASE) {
                // // 只在状态刚从释放变为短按时触发
                sm_state.key_pressed = true;
                DL_GPIO_togglePins(USER_LED_1_PORT, USER_LED_1_PIN_A_0_PIN);
                sprintf(uart_buffer, "Key: Motor speed increase requested\r\n");
                UART_SendString(uart_buffer);
            }
            break;

        case KEY_EVENT_USER_LONGPRESS:
            // 用户按键长按 - 设置电机停止标志位
            if (prevKeyEvent != KEY_EVENT_USER_LONGPRESS) {
                // 只在首次检测到长按时设置标志位
                sm_state.key_long_pressed = true;
                // 启动LED闪烁（非阻塞）
                led_blink_active = true;
                led_blink_time = get_tick_ms();
                sprintf(uart_buffer, "Key: Motor stop requested\r\n");
                UART_SendString(uart_buffer);
            }
            break;

        case KEY_EVENT_USER_RELEASE:
            // 按键释放 - 停止LED闪烁
            led_blink_active = false;
            break;

        default:
            break;
    }

    // 处理第二个按键事件 - 只设置标志位
    switch (currentKeyEvent1) {
        case KEY_EVENT_USER_PRESS:
            // 第二个按键短按 - 不做特殊处理
            break;

        case KEY_EVENT_USER_LONGPRESS:
            // 第二个按键长按 - 设置编码器重置标志位
            if (prevKeyEvent1 != KEY_EVENT_USER_LONGPRESS) {
                // 只在首次检测到长按时设置标志位
                key_encoder_reset = true;
                sprintf(uart_buffer, "Key: Encoder reset requested\r\n");
                UART_SendString(uart_buffer);
            }
            break;

        default:
            break;
    }
}

/**
 * @brief 非阻塞LED闪烁处理
 */
void Handle_LED_Blink_NonBlocking(void) {
    if (!led_blink_active) {
        return;
    }

    uint32_t current_time = get_tick_ms();
    if (current_time - led_blink_time >= LED_BLINK_INTERVAL) {
        DL_GPIO_togglePins(USER_LED_1_PORT, USER_LED_1_PIN_A_0_PIN);
        led_blink_time = current_time;
    }
}

/**
 * @brief 非阻塞ADC采样处理
 *
 * 使用状态机方式实现非阻塞ADC采样：
 * 1. 检查是否到达采样时间间隔
 * 2. 如果是，启动ADC转换
 * 3. 检查ADC转换是否完成
 * 4. 如果完成，获取结果并更新adc_value
 */
void Handle_ADC_Sampling_NonBlocking(void) {
    static enum {
        ADC_IDLE,
        ADC_CONVERTING
    } adc_state = ADC_IDLE;

    uint32_t current_time = get_tick_ms();

    switch (adc_state) {
        case ADC_IDLE:
            // 检查是否到达采样时间间隔
            if (current_time - last_adc_sample_time >= ADC_SAMPLE_INTERVAL) {
                // 启动ADC转换
                ADC_StartConversion(0);
                adc_state = ADC_CONVERTING;
            }
            break;

        case ADC_CONVERTING:
            // 检查ADC转换是否完成
            if (ADC_IsConversionComplete()) {
                // 获取结果并更新adc_value
                adc_value = ADC_GetLastResult();

                // 更新采样时间
                last_adc_sample_time = current_time;

                // 返回空闲状态
                adc_state = ADC_IDLE;
            }
            break;
    }
}

// DMA中断计数器（用于调试）
volatile uint32_t dma_ch1_interrupt_count = 0;

/**
 * @brief DMA中断服务程序
 */
void DMA_IRQHandler(void)
{
    switch (DL_DMA_getPendingInterrupt(DMA)) {
        case DL_DMA_EVENT_IIDX_DMACH0:
            // UART DMA传输完成
            break;

        case DL_DMA_EVENT_IIDX_DMACH1:
            // 电机ADC DMA传输完成
            dma_ch1_interrupt_count++;  // 调试计数
            ADC_DMA_TransferComplete();
            break;

        case DL_DMA_EVENT_IIDX_DMACH2:
            // 示波器ADC DMA传输完成            
            osc_dma_interrupt_count++; // 增加中断计数
            Oscilloscope_DMAComplete();
            break;

        default:
            break;
    }
}
