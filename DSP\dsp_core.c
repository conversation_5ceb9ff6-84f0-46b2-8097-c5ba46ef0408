/**
 * @file dsp_core.c
 * @brief DSP核心模块实现
 * @version 1.0
 * @date 2025-01-29
 */

#include "dsp_config.h"
#include <stdlib.h>

/* 性能测量变量 */
#if DSP_DEBUG_ENABLE
uint32_t dsp_perf_start_time = 0;
#endif

/**
 * @brief 检查FFT大小是否有效
 */
bool DSP_IsValidFFTSize(uint16_t fft_size)
{
    /* 检查是否在支持范围内 */
    if (fft_size < DSP_FFT_MIN_SIZE || fft_size > DSP_FFT_MAX_SIZE) {
        return false;
    }
    
    /* 检查是否是2的幂次 */
    return (fft_size > 0) && ((fft_size & (fft_size - 1)) == 0);
}
