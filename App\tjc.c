/**
 * @file tjc.c
 * @brief TJC串口屏通信协议实现
 */

#include "tjc.h"
#include "motor.h"
#include "usart.h"
#include "oscilloscope.h"
#include <stdio.h>

// 定义TJC解析器实例
TJC_Parser tjc_parser;

/**
 * @brief 初始化TJC协议解析器
 * @param parser 解析器指针
 */
void TJC_ParserInit(TJC_Parser* parser)
{
    parser->state = TJC_STATE_WAIT_START;
    parser->dataIndex = 0;
}

/**
 * @brief 处理接收到的字节
 * @param parser 解析器指针
 * @param byte 接收到的字节
 * @param callback 帧接收完成回调函数
 * @return 是否成功解析到一个完整帧
 */
bool TJC_ParseByte(TJC_Parser* parser, uint8_t byte, TJC_FrameCallback callback)
{
    bool frameComplete = false;

    switch (parser->state) {
        case TJC_STATE_WAIT_START:
            if (byte == TJC_FRAME_START) {
                parser->state = TJC_STATE_WAIT_PAGE_ID;
            }
            break;

        case TJC_STATE_WAIT_PAGE_ID:
            parser->frame.page_id = byte;
            parser->state = TJC_STATE_WAIT_COMMAND;
            break;
            
        case TJC_STATE_WAIT_COMMAND:
            parser->frame.command = byte;
            parser->state = TJC_STATE_WAIT_LENGTH;
            break;

        case TJC_STATE_WAIT_LENGTH:
            parser->frame.length = byte;
            parser->dataIndex = 0;
            
            if (parser->frame.length > 0) {
                parser->state = TJC_STATE_WAIT_DATA;
            } else {
                parser->state = TJC_STATE_WAIT_END;
            }
            break;

        case TJC_STATE_WAIT_DATA:
            if (parser->dataIndex < parser->frame.length && 
                parser->dataIndex < TJC_MAX_DATA_SIZE) {
                parser->frame.data[parser->dataIndex++] = byte;
                
                if (parser->dataIndex >= parser->frame.length) {
                    parser->state = TJC_STATE_WAIT_END;
                }
            }
            break;

        case TJC_STATE_WAIT_END:
            if (byte == TJC_FRAME_END) {
                frameComplete = true;
                
                // 回调通知帧接收完成
                if (callback != NULL) {
                    callback(&parser->frame);
                }
            }
            
            // 无论结束符是否正确，都重置到等待起始状态
            parser->state = TJC_STATE_WAIT_START;
            break;

        default:
            parser->state = TJC_STATE_WAIT_START;
            break;
    }

    return frameComplete;
}
