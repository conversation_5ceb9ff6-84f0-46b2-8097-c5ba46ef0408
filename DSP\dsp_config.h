/**
 * @file dsp_config.h
 * @brief CMSIS-DSP配置文件
 * @version 1.0
 * @date 2025-01-29
 */

#ifndef __DSP_CONFIG_H__
#define __DSP_CONFIG_H__

#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/* CMSIS-DSP库配置 */
#define ARM_MATH_CM0PLUS                // 指定Cortex-M0+内核
#define ARM_MATH_ROUNDING               // 启用舍入功能

/* 数据类型配置 */
#define DSP_USE_FLOAT32                 // 使用32位浮点运算

/* FFT配置参数 */
#define DSP_FFT_MAX_SIZE        1024    // 最大FFT点数
#define DSP_FFT_DEFAULT_SIZE    512     // 默认FFT点数
#define DSP_FFT_MIN_SIZE        64      // 最小FFT点数

/* 支持的FFT长度 */
#define DSP_FFT_SIZE_64         64
#define DSP_FFT_SIZE_128        128
#define DSP_FFT_SIZE_256        256
#define DSP_FFT_SIZE_512        512
#define DSP_FFT_SIZE_1024       1024

/* 采样率配置 */
#define DSP_SAMPLE_RATE_8K      8000    // 8kHz采样率
#define DSP_SAMPLE_RATE_16K     16000   // 16kHz采样率
#define DSP_SAMPLE_RATE_32K     32000   // 32kHz采样率
#define DSP_SAMPLE_RATE_48K     48000   // 48kHz采样率
#define DSP_SAMPLE_RATE_DEFAULT DSP_SAMPLE_RATE_16K

/* 调试配置 */
#ifdef DEBUG
    #define DSP_DEBUG_ENABLE    1
    #define DSP_ASSERT_ENABLE   1
#else
    #define DSP_DEBUG_ENABLE    0
    #define DSP_ASSERT_ENABLE   0
#endif

/* DSP状态定义 */
typedef enum {
    DSP_STATUS_OK = 0,                  // 操作成功
    DSP_STATUS_ERROR,                   // 一般错误
    DSP_STATUS_INVALID_PARAM,           // 参数无效
    DSP_STATUS_INSUFFICIENT_MEMORY,     // 内存不足
    DSP_STATUS_NOT_INITIALIZED,         // 未初始化
    DSP_STATUS_BUSY,                    // 忙碌状态
    DSP_STATUS_TIMEOUT                  // 超时
} DSP_Status;

/* DSP数据类型定义 */
#ifdef DSP_USE_FLOAT32
    typedef float DSP_Float;
#endif

/* 窗函数类型定义 */
typedef enum {
    DSP_WINDOW_RECTANGULAR = 0,         // 矩形窗
    DSP_WINDOW_HANNING,                 // 汉宁窗
    DSP_WINDOW_HAMMING,                 // 海明窗
    DSP_WINDOW_BLACKMAN,                // 布莱克曼窗
    DSP_WINDOW_KAISER                   // 凯泽窗
} DSP_WindowType;

/* 平台相关的内存分配 */
#define DSP_MALLOC(size)        malloc(size)
#define DSP_FREE(ptr)           free(ptr)

/* 调试宏定义 */
#if DSP_DEBUG_ENABLE
    #include <stdio.h>
    #define DSP_DEBUG(fmt, ...)     printf("[DSP] " fmt "\r\n", ##__VA_ARGS__)
    #define DSP_ERROR(fmt, ...)     printf("[DSP ERROR] " fmt "\r\n", ##__VA_ARGS__)
#else
    #define DSP_DEBUG(fmt, ...)
    #define DSP_ERROR(fmt, ...)
#endif

/* 断言宏定义 */
#if DSP_ASSERT_ENABLE
    #define DSP_ASSERT(expr)        \
        do { \
            if (!(expr)) { \
                DSP_ERROR("Assertion failed: %s, file %s, line %d", \
                         #expr, __FILE__, __LINE__); \
                while(1); \
            } \
        } while(0)
#else
    #define DSP_ASSERT(expr)
#endif

/* 性能测量宏 */
#if DSP_DEBUG_ENABLE
    extern uint32_t dsp_perf_start_time;
    #define DSP_PERF_START()    (dsp_perf_start_time = 0) // 需要实现时间函数
    #define DSP_PERF_END(name)  DSP_DEBUG("Performance [%s]: completed", name)
#else
    #define DSP_PERF_START()
    #define DSP_PERF_END(name)
#endif

/* 数学常数定义 */
#ifndef PI
#define PI                      3.14159265358979323846f
#endif
#define TWO_PI                  (2.0f * PI)

/* 工具函数声明 */
bool DSP_IsValidFFTSize(uint16_t fft_size);

#endif /* __DSP_CONFIG_H__ */
