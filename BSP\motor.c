/*
 * motor.c
 * H桥电机驱动实现文件
 */
#include "motor.h"
#include "usart.h"
#include <stdio.h>

#define motor_e1_a 0
#define motor_e1_b 1

int motor_speed = 0;// 电机速度全局变量
bool motor_init = false;
uint32_t period = 32000;
extern char uart_buffer[1000];

/**
 * @brief 设置指定通道PWM占空比函数
 * 
 * @param duty 占空比%
 * @param channel 指定的pwm通道
 */
void set_pwm_duty(float duty,int channel){
    uint32_t CompareValue;
    CompareValue =  period-period*duty;

    if(channel==0){
        /* 这个函数存在于TI库中，保留使用 */
        DL_Timer_setCaptureCompareValue(PWM_MOTOER_A_INST, CompareValue, DL_TIMER_CC_0_INDEX);
    }else{
        DL_Timer_setCaptureCompareValue(PWM_MOTOER_A_INST, CompareValue, DL_TIMER_CC_1_INDEX);
    }  
}

/**
 * @brief 初始化电机
 */
void MOTOR_Init(void){
    // pwm 频率1khz 占空比0%
    DL_Timer_startCounter(PWM_MOTOER_A_INST);
    
    // 初始化时确保电机停止
    set_pwm_duty(0, motor_e1_a);
    set_pwm_duty(0, motor_e1_b);
    motor_speed = 0;
    
    // 清除可能的事件标志
    DL_Timer_clearEventsStatus(PWM_MOTOER_A_INST, 0, 0xFFFFFFFF);
}

/**
 * @brief 设置电机速度
 * 
 * @param speed 指定电机转速百分比，-100~+100
 */
void set_motor_speed(int speed){
    // 更新全局变量
    motor_speed = speed;

    // 限制速度范围在-100到100之间
    if(motor_speed >= 100) {
        motor_speed = 100;
    } else if(motor_speed <= -100) {
        motor_speed = -100;
    }

    // 添加调试信息

    sprintf(uart_buffer, "set_motor_speed called: %d%% -> PWM duty: %.2f\r\n",
            motor_speed, motor_speed/100.0);
    UART_SendString(uart_buffer);

    // 根据速度设置PWM值
    if(motor_speed == 0) {
        // 停止：两个通道都设置为0
        set_pwm_duty(0, motor_e1_a);
        set_pwm_duty(0, motor_e1_b);
        UART_SendString("Motor stopped - both channels OFF\r\n");
    } else if(motor_speed > 0) {
        // 正转：使用A通道，B通道为0
        set_pwm_duty(motor_speed/100.0, motor_e1_a);
        set_pwm_duty(0, motor_e1_b);
        sprintf(uart_buffer, "Motor forward - Channel A: %.2f%%, Channel B: 0%%\r\n",
                motor_speed/100.0 * 100);
        UART_SendString(uart_buffer);
    } else {
        // 反转：使用B通道，A通道为0
        set_pwm_duty(0, motor_e1_a);
        set_pwm_duty(-(motor_speed/100.0), motor_e1_b);
        sprintf(uart_buffer, "Motor reverse - Channel A: 0%%, Channel B: %.2f%%\r\n",
                -(motor_speed/100.0) * 100);
        UART_SendString(uart_buffer);
    }
}