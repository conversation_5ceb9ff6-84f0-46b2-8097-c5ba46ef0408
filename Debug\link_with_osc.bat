@echo off
"F:/Ti/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" @"device.opt" -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -gdwarf-3 -Wl,-m"key.map" -Wl,-i"F:/Ti/ccs/mspm0_sdk_2_05_00_05/source" -Wl,-i"F:/Ti/work/key" -Wl,-i"F:/Ti/work/key/Debug/syscfg" -Wl,-i"F:/Ti/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib" -Wl,--diag_wrap=off -Wl,--display_error_number -Wl,--warn_sections -Wl,--xml_link_info="key_linkInfo.xml" -Wl,--rom_model -o "key.out" "./main.o" "./ti_msp_dl_config.o" "./startup_mspm0g350x_ticlang.o" "./App/oscilloscope.o" "./App/send_info.o" "./App/state_machine.o" "./App/tjc.o" "./BSP/adc.o" "./BSP/delay.o" "./BSP/key.o" "./BSP/motor.o" "./BSP/time.o" "./BSP/usart.o" -Wl,-l"./device_linker.cmd" -Wl,-ldevice.cmd.genlibs -Wl,-llibc.a
echo Linking complete
