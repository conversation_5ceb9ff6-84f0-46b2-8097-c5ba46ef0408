# 系统架构说明文档

## 项目概述
本项目是基于MSPM0G3507微控制器和陶晶驰7寸串口屏的触屏交互系统，实现了电机控制、示波器功能和数据可视化等多种功能。

## 核心架构设计

### 1. 分层架构
```
┌─────────────────────────────────────┐
│           应用层 (App)              │
├─────────────────────────────────────┤
│        板级支持包 (BSP)             │
├─────────────────────────────────────┤
│         硬件抽象层 (HAL)            │
├─────────────────────────────────────┤
│          硬件层 (MCU)               │
└─────────────────────────────────────┘
```

**应用层 (App)**：
- `state_machine.c/h` - 核心状态机调度器
- `tjc.c/h` - TJC串口屏通信协议
- `oscilloscope.c/h` - 示波器功能实现
- `send_info.c/h` - 数据发送管理

**板级支持包 (BSP)**：
- `usart.c/h` - UART通信驱动
- `motor.c/h` - 电机控制驱动
- `adc.c/h` - ADC采样驱动
- `key.c/h` - 按键处理驱动
- `time.c/h` - 编码器和定时器驱动
- `delay.c/h` - 延时和时间管理

### 2. 状态机核心架构

#### 2.1 状态机数据结构
```c
typedef struct {
    uint8_t current_page_id;        // 当前页面ID
    bool new_command_received;      // 新命令接收标志
    TJC_Frame received_frame;       // 接收到的命令帧
} StateMachine_State;
```

#### 2.2 页面状态定义
- `PAGE_MAIN (0)` - 主界面，负责界面跳转
- `PAGE_MOTOR (1)` - 电机控制界面，处理电机相关命令
- `PAGE_OSCILLOSCOPE (2)` - 示波器界面，处理示波器相关命令

#### 2.3 命令类型分类
- `CMD_NAVIGATION (0x01)` - 界面跳转命令
- `CMD_MOTOR_CONTROL (0x02)` - 电机控制命令
- `CMD_OSCILLOSCOPE (0x03)` - 示波器命令

### 3. 通信协议架构

#### 3.1 TJC协议帧格式
```
┌──────┬──────────┬──────────┬──────────┬──────────┬──────┐
│ 0x61 │ Page ID  │ Command  │ Length   │   Data   │ 0xFF │
│(帧头) │(页面ID)  │(命令类型)│(数据长度)│(数据内容)│(帧尾)│
└──────┴──────────┴──────────┴──────────┴──────────┴──────┘
```

#### 3.2 协议解析状态机
```
等待帧头 → 等待页面ID → 等待命令 → 等待长度 → 等待数据 → 等待帧尾
    ↓           ↓          ↓        ↓        ↓        ↓
  0x61      Page ID    Command   Length    Data     0xFF
```

### 4. 主循环调度架构

#### 4.1 非阻塞任务调度
```c
while (1) {
    // 按键扫描 - 每10ms
    if (current_time - last_key_scan_time >= KEY_SCAN_INTERVAL) {
        KET_Event_NonBlocking();
    }
    
    // 状态机处理 - 实时
    StateMachine_Process();
    
    // 串口数据处理 - 实时
    TJC_ProcessReceivedData();
    
    // LED控制 - 非阻塞
    Handle_LED_Blink_NonBlocking();
    
    // ADC采样 - 非阻塞
    Handle_ADC_Sampling_NonBlocking();
}
```

#### 4.2 状态机处理流程
```
StateMachine_Process()
        ↓
处理按键事件
        ↓
处理数据发送
        ↓
检查新命令标志
        ↓
根据页面ID分发命令
        ↓
执行对应页面处理函数
        ↓
清除命令标志
```

### 5. 数据流架构

#### 5.1 命令数据流
```
串口屏 → UART+DMA → 接收缓冲区 → TJC解析器 → 状态机 → 功能模块
```

#### 5.2 显示数据流
```
功能模块 → 数据格式化 → UART发送 → 串口屏显示
```

### 6. 内存管理架构

#### 6.1 静态内存分配
- 全局缓冲区：`uart_buffer[1000]` - UART发送缓冲区
- DMA缓冲区：`gRxPacket[UART_PACKET_SIZE]` - UART接收缓冲区
- 示波器缓冲区：`ch1_buffer[OSC_BUFFER_SIZE]` - 波形数据缓冲区

#### 6.2 栈内存使用
- 局部变量和函数调用栈
- 中断服务程序栈空间

### 7. 中断处理架构

#### 7.1 中断优先级
1. SysTick中断 - 系统时钟，最高优先级
2. UART DMA中断 - 数据接收处理
3. ADC DMA中断 - 示波器数据采集
4. 定时器中断 - PWM和编码器处理

#### 7.2 中断与主循环协作
- 中断负责数据采集和标志设置
- 主循环负责数据处理和逻辑执行
- 通过标志位实现中断与主循环的同步

## 扩展开发指南

### 添加新功能的标准流程

1. **需求分析**
   - 确定功能所属的页面类型
   - 定义所需的命令类型和数据格式

2. **协议层扩展**
   - 在 `tjc.h` 中添加新的命令常量定义
   - 如需新页面，添加页面ID常量

3. **状态机层扩展**
   - 在 `state_machine.h` 中添加函数声明
   - 在 `state_machine.c` 中实现页面处理函数

4. **功能层实现**
   - 创建或修改对应的功能模块文件
   - 实现具体的业务逻辑

5. **数据发送扩展**
   - 在 `StateMachine_HandleDataSending()` 中添加数据发送逻辑
   - 实现与串口屏的数据交互

6. **测试验证**
   - 编写测试用例验证功能正确性
   - 进行集成测试确保系统稳定性

### 代码规范建议

1. **命名规范**
   - 函数名：`ModuleName_FunctionName()`
   - 变量名：`snake_case` 或 `camelCase`
   - 常量名：`UPPER_CASE_WITH_UNDERSCORES`

2. **文件组织**
   - 每个功能模块包含 `.c` 和 `.h` 文件
   - 头文件包含接口声明，源文件包含实现
   - 使用头文件保护宏防止重复包含

3. **注释规范**
   - 使用 Doxygen 风格的函数注释
   - 关键算法和复杂逻辑添加详细说明
   - 文件头部包含版本信息和修改记录

## 性能优化建议

1. **内存优化**
   - 合理设置缓冲区大小，避免内存浪费
   - 使用内存池管理动态内存分配
   - 定期检查栈使用情况，防止栈溢出

2. **实时性优化**
   - 关键任务使用中断处理，提高响应速度
   - 非关键任务使用轮询方式，降低系统负载
   - 合理设置任务调度间隔，平衡性能和功耗

3. **通信优化**
   - 使用DMA减少CPU占用
   - 批量发送数据，减少通信开销
   - 实现数据压缩和缓存机制

这个架构设计确保了系统的可扩展性、可维护性和实时性，为后续功能开发提供了坚实的基础。
