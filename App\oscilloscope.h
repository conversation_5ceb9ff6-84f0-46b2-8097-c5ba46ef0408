/**
 * @file oscilloscope.h
 * @brief 示波器功能模块头文件
 * @version 1.0
 * @date 2025-01-17
 * 
 * 本文件定义了示波器的数据结构、接口函数和配置参数
 * 支持双通道波形采集、触发检测、测量功能等
 */

#ifndef __OSCILLOSCOPE_H__
#define __OSCILLOSCOPE_H__

#include <stdint.h>
#include <stdbool.h>
#include "ti_msp_dl_config.h"
#include "tjc.h"

/* 示波器配置参数 */
#define OSC_BUFFER_SIZE         200    // 波形数据缓冲区大小
#define OSC_DISPLAY_POINTS      150     // 显示点数
#define OSC_TRIGGER_TIMEOUT     1000    // 触发超时时间(ms)
#define OSC_SAMPLE_RATE_DEFAULT 31250   // 默认采样率(Hz)

/* 数据发送频率控制参数 */
#define OSC_SEND_INTERVAL_MIN   100     // 最小发送间隔(ms)
#define OSC_SEND_INTERVAL_MAX   10000   // 最大发送间隔(ms)
#define OSC_SEND_INTERVAL_DEFAULT 1000  // 默认发送间隔(ms)

/* 波形显示参数 */
#define OSC_REALY_HEIGHT        (double)355     // 波形显示区域实际高度(像素)
#define OSC_DISPLAY_HEIGHT      (double)255     // 波形显示区域映射高度
#define OSC_WAVEFORM_OBJECT     "s0.id" // 波形控件名称
#define OSC_WAVEFORM_CHANNEL    0       // 波形通道号
#define OSC_PATTERN_HEIGHT      60      // 上位机每个的像素高度

/* 示波器命令定义 - 使用tjc.h中已定义的OscilloscopeCommand枚举 */

/* 页面状态定义 - 与tjc.h保持一致 */
#ifndef PAGE_MAIN
#define PAGE_MAIN 0          // 主界面 - 只负责界面跳转
#define PAGE_MOTOR 1         // 电机控制界面 - 处理电机相关命令  
#define PAGE_OSCILLOSCOPE 2  // 示波器界面 - 处理示波器相关命令
#endif

/* 命令类型定义 - 与tjc.h保持一致 */
#ifndef CMD_NAVIGATION
#define CMD_NAVIGATION 0x01     // 界面跳转命令
#define CMD_MOTOR_CONTROL 0x02  // 电机控制命令
#define CMD_OSCILLOSCOPE 0x03   // 示波器命令
#endif

/* 触发模式定义 */
typedef enum {
    OSC_TRIGGER_AUTO = 0,         // 自动触发
    OSC_TRIGGER_NORMAL = 1,       // 正常触发
    OSC_TRIGGER_SINGLE = 2        // 单次触发
} OscTriggerMode;

/* 触发边沿定义 */
typedef enum {
    OSC_TRIGGER_RISING = 0,       // 上升沿触发
    OSC_TRIGGER_FALLING = 1       // 下降沿触发
} OscTriggerEdge;

/* 运行状态定义 */
typedef enum {
    OSC_STATE_STOP = 0,           // 停止状态
    OSC_STATE_RUN = 1,            // 运行状态
    OSC_STATE_SINGLE = 2,         // 单次采集状态
    OSC_STATE_WAIT_TRIGGER = 3    // 等待触发状态
} OscRunState;

/* 通道配置结构 */
typedef struct {
    bool enabled;                 // 通道使能
    float voltage_scale;          // 电压量程 (V/格)
    float offset;                 // 垂直偏移
    uint16_t coupling;            // 耦合方式 (DC/AC)
} OscChannelConfig;

/* 触发配置结构 */
typedef struct {
    OscTriggerMode mode;          // 触发模式
    OscTriggerEdge edge;          // 触发边沿
    uint8_t source;               // 触发源 (0=CH1, 1=CH2)
    double level;                 // 触发电平 (ADC值)
    uint16_t holdoff;             // 触发抑制时间
} OscTriggerConfig;

/* 时基配置结构 */
typedef struct {
    float time_per_div;           // 时间/格 (s/格)
    uint32_t sample_rate;         // 采样率 (Hz)
    uint16_t pre_trigger;         // 预触发点数
} OscTimebaseConfig;

/* 测量结果结构 */
typedef struct {
    float frequency;              // 频率 (Hz)
    float period;                 // 周期 (s)
    float peak_to_peak;           // 峰峰值 (V)
    float rms;                    // 有效值 (V)
    float mean;                   // 平均值 (V)
    float max_value;              // 最大值 (V)
    float min_value;              // 最小值 (V)
    bool valid;                   // 测量结果有效标志
} OscMeasurement;

/* 示波器主配置结构 */
typedef struct {
    OscRunState state;            // 运行状态
    OscChannelConfig ch1;         // 通道1配置
    OscChannelConfig ch2;         // 通道2配置
    OscTriggerConfig trigger;     // 触发配置
    OscTimebaseConfig timebase;   // 时基配置
    OscMeasurement measurement;   // 测量结果
    
    // 数据缓冲区
    uint16_t ch1_buffer[OSC_BUFFER_SIZE];     // CH1数据缓冲区
    uint16_t ch2_buffer[OSC_BUFFER_SIZE];     // CH2数据缓冲区
    uint16_t display_buffer[OSC_DISPLAY_POINTS]; // 显示缓冲区
    
    // 状态标志
    bool data_ready;              // 数据就绪标志
    bool trigger_found;           // 触发检测标志
    uint16_t buffer_index;        // 缓冲区索引
    uint16_t trigger_index;       // 触发位置索引
    uint32_t sample_count;        // 采样计数
    
    // 统计信息
    uint32_t trigger_count;       // 触发次数
    uint32_t missed_trigger;      // 丢失触发次数

    // 数据发送频率控制
    uint32_t send_interval;       // 发送间隔(ms)
    uint32_t last_send_time;      // 上次发送时间戳
    bool auto_send_enabled;       // 自动发送使能
} OscilloscopeConfig;

/* 外部变量声明 */
extern OscilloscopeConfig osc_config;
extern char uart_buffer[1000];

/* 主要接口函数 */

/**
 * @brief 初始化示波器模块
 */
void Oscilloscope_Init(void);

/**
 * @brief 启动示波器采集
 */
void Oscilloscope_Start(void);

/**
 * @brief 停止示波器采集
 */
void Oscilloscope_Stop(void);

/**
 * @brief 强制触发
 */
void Oscilloscope_ForceTrigger(void);

/**
 * @brief 单次触发
 */
void Oscilloscope_SingleTrigger(void);

/**
 * @brief 清除波形数据
 */
void Oscilloscope_ClearWaveform(void);

/* 配置函数 */

/**
 * @brief 设置时基
 * @param time_per_div 时间/格 (秒)
 */
void Oscilloscope_SetTimebase(float time_per_div);

/**
 * @brief 设置通道1量程
 * @param voltage_scale 电压/格 (伏特)
 */
void Oscilloscope_SetCH1Scale(float voltage_scale);

/**
 * @brief 设置通道2量程
 * @param voltage_scale 电压/格 (伏特)
 */
void Oscilloscope_SetCH2Scale(float voltage_scale);

/**
 * @brief 设置触发配置
 * @param mode 触发模式
 * @param edge 触发边沿
 * @param level 触发电平 (0-4095)
 */
void Oscilloscope_SetTrigger(OscTriggerMode mode, OscTriggerEdge edge, uint16_t level);

/* 数据处理函数 */

/**
 * @brief ADC数据处理回调函数
 * @param ch1_data CH1 ADC数据
 * @param ch2_data CH2 ADC数据
 */
void Oscilloscope_DataCallback(uint16_t ch1_data, uint16_t ch2_data);

/**
 * @brief 波形测量函数
 * 计算频率、峰峰值等参数
 */
void Oscilloscope_MeasureWaveform(void);

/* 数据传输函数 */

/**
 * @brief 发送波形数据到上位机
 */
void Oscilloscope_SendWaveformData(void);

/**
 * @brief 发送测量结果到上位机
 */
void Oscilloscope_SendMeasurement(void);

/**
 * @brief 发送示波器状态到上位机
 */
void Oscilloscope_SendStatus(void);

/**
 * @brief 设置数据发送间隔
 * @param interval_ms 发送间隔(毫秒)，范围: OSC_SEND_INTERVAL_MIN ~ OSC_SEND_INTERVAL_MAX
 */
void Oscilloscope_SetSendInterval(uint32_t interval_ms);



/**
 * @brief 启用/禁用自动发送
 * @param enabled true: 启用自动发送, false: 禁用自动发送
 */
void Oscilloscope_SetAutoSend(bool enabled);

/* 工具函数 */

/**
 * @brief ADC值转换为电压值
 * @param adc_value ADC原始值 (0-4095)
 * @param channel 通道号 (0=CH1, 1=CH2)
 * @return 电压值 (伏特)
 */
float Oscilloscope_ADCToVoltage(uint16_t adc_value, uint8_t channel);

/**
 * @brief 电压值转换为ADC值
 * @param voltage 电压值 (伏特)
 * @param channel 通道号 (0=CH1, 1=CH2)
 * @return ADC值 (0-4095)
 */
uint16_t Oscilloscope_VoltageToADC(float voltage, uint8_t channel);

/**
 * @brief DMA传输完成中断处理函数
 * 需要在中断服务程序中调用
 */
void Oscilloscope_DMAComplete(void);

/**
 * @brief 尝试读取真实ADC数据
 */
void Oscilloscope_ReadRealADC(void);

#endif /* __OSCILLOSCOPE_H__ */
