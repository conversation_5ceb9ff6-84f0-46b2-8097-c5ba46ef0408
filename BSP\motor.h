/*
 * motor.h
 * H桥电机驱动头文件
 * 实现正转，反转，急停
 * AIN1 PA12 
 * AIN2 PA13
 * TIMEG0
 */
#ifndef MOTOR_H_
#define MOTOR_H_

#include "ti_msp_dl_config.h"
/**
 * @brief 设置指定通道PWM占空比函数
 * 
 * @param duty 占空比%
 * @param channel 指定的pwm通道
 */
void set_pwm_duty(float duty,int channel);

/**
 * @brief 初始化电机
 */
void MOTOR_Init(void);

/**
 * @brief 设置电机速度
 * 
 * @param speed 指定电机转速百分比，-100~+100
 */
void set_motor_speed(int speed);

#endif
