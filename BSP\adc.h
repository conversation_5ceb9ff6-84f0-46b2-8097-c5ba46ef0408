/*
 * ADC.h
 * ADC模块化处理头文件
 * ADC输入通道0 PA25
 */

#ifndef ADC_H_
#define ADC_H_

#include "ti_msp_dl_config.h"

// ADC通道定义
typedef enum {
    ADC_CHANNEL_0 = 0,
    ADC_CHANNEL_1,
    ADC_CHANNEL_2,
    ADC_CHANNEL_3,
    // 可根据需要添加更多通道
} ADC_Channel_t;

/**
 * @brief 初始化ADC模块
 */
void ADC_Init(void);

/**
 * @brief 获取指定通道的ADC值（阻塞式）
 * @param channel ADC通道
 * @return int 转换结果
 */
int ADC_GetValue(ADC_Channel_t channel);

/**
 * @brief 启动ADC转换（非阻塞）
 * @param channel ADC通道
 */
void ADC_StartConversion(ADC_Channel_t channel);

/**
 * @brief 检查ADC转换是否完成
 * @return true 转换完成，false 转换进行中
 */
bool ADC_IsConversionComplete(void);

/**
 * @brief 获取最后一次ADC转换结果（非阻塞）
 * @return int 转换结果
 */
int ADC_GetLastResult(void);

/**
 * @brief ADC DMA传输完成处理函数
 * 这个函数将被main.c中的DMA_IRQHandler调用
 */
void ADC_DMA_TransferComplete(void);

#endif /* ADC_H_ */