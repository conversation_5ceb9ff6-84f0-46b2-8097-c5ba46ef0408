/*
 * time.c
 * 串口模块化处理实现文件
 */

/* 包含我们自定义的类型和环境 */
#include "bsp_types.h"
#include "time.h"
#include "delay.h"
#include "ti_msp_dl_config.h"

/* 全局变量 */
static int32_t lastPosition = 0;  // 上一次编码器位置
static uint32_t lastTime = 0;     // 上一次读取时间 (ms)
static int32_t encoderSpeed = 0;  // 编码器速度 (pulse/s)
static uint8_t encoderDirection = QEI_DIR_FWD;  // 编码器方向

/**
 * @brief 初始化定时器为QEI模式
 */
void timer_init(void) {
    /* 
     * 注意: GPIO引脚设置和QEI模式配置已由SysConfig完成
     * 在ti_msp_dl_config.c中的SYSCFG_DL_GPIO_init和SYSCFG_DL_USER_QEI_0_init函数
     * 这里只需启动计数器并初始化我们的变量
     */
    
    /* 确保定时器已停止 */
    DL_TimerG_stopCounter(USER_QEI_0_INST);

    /* 清零计数器 */
    DL_TimerG_setTimerCount(USER_QEI_0_INST, 0);

    /* 启动定时器 */
    DL_TimerG_startCounter(USER_QEI_0_INST);
    
    /* 初始化时间基准 */
    lastTime = get_tick_ms();
    lastPosition = 0;
}

/**
 * @brief 获取编码器当前位置
 * @return 编码器位置计数值
 */
int32_t QEI_GetPosition(void) {
    return (int32_t)DL_TimerG_getTimerCount(USER_QEI_0_INST);
}

/**
 * @brief 获取编码器旋转方向
 * @return QEI_DIR_FWD:正向旋转 QEI_DIR_REV:反向旋转
 */
uint8_t QEI_GetDirection(void) {
    return encoderDirection;
}

/**
 * @brief 重置编码器计数值
 */
void QEI_Reset(void) {
    DL_TimerG_stopCounter(USER_QEI_0_INST);
    DL_TimerG_setTimerCount(USER_QEI_0_INST, 0);
    DL_TimerG_startCounter(USER_QEI_0_INST);

    lastPosition = 0;
    lastTime = get_tick_ms();
    encoderSpeed = 0;
}

/**
 * @brief 计算编码器速度，需要周期性调用
 * @return 编码器速度 (pulse/s)
 */
int32_t QEI_GetSpeed(void) {
    int32_t currentPosition = QEI_GetPosition();
    uint32_t currentTime = get_tick_ms();  // 获取当前系统时间(ms)
    uint32_t deltaTime = currentTime - lastTime;
    
    /* 至少需要10ms才更新一次速度，避免频繁计算 */
    if (deltaTime >= 10) {
        /* 计算位置变化和速度 */
        int32_t deltaPosition = currentPosition - lastPosition;
        
        /* 计算速度 (脉冲/秒) */
        if (deltaTime > 0) {
            encoderSpeed = (deltaPosition * 1000) / deltaTime;
        }
        
        /* 确定方向 */
        if (encoderSpeed > 0) {
            encoderDirection = QEI_DIR_FWD;
        } else if (encoderSpeed < 0) {
            encoderDirection = QEI_DIR_REV;
        }
        
        /* 更新上一次的位置和时间 */
        lastPosition = currentPosition;
        lastTime = currentTime;
    }
    
    return encoderSpeed;
}
