# 示波器命令优化说明

## 优化目标
根据`docs/要求.md`文档中的规定，上位机只会发送6个示波器命令，因此需要删除不必要的调试和测试命令，确保代码符合系统架构要求。

## 文档要求的命令
根据`docs/要求.md`第58-66行，上位机会发送的命令有：
1. RUN/STOP - 运行/停止
2. SINGLE - 单次触发
3. AUTO - 自动触发
4. FORCE - 强制触发
5. CLEAR - 清除波形
6. MEASURE - 测量

## 优化前的问题
原始代码中定义了15个示波器命令，包含了许多文档中未要求的调试和测试命令：
- `OSC_CMD_SET_SCALE` - 设置量程
- `OSC_CMD_TRIGGER` - 触发设置
- `OSC_CMD_SET_INTERVAL` - 设置发送间隔
- `OSC_CMD_AUTO_SEND` - 启用/禁用自动发送
- `OSC_CMD_DEBUG` - 调试状态检查
- `OSC_CMD_TEST_DATA` - 强制填充测试数据
- `OSC_CMD_REAL_ADC` - 读取真实ADC数据
- `OSC_CMD_SEND_WAVEFORM` - 发送波形数据到上位机

## 优化内容

### 1. 命令定义优化 (`App/tjc.h`)
**优化前**：
```c
typedef enum {
    OSC_CMD_START = 0x01,       // RUN - 开始采样
    OSC_CMD_STOP = 0x02,        // STOP - 停止采样
    OSC_CMD_SET_SCALE = 0x03,   // 设置量程
    OSC_CMD_TRIGGER = 0x04,     // 触发设置
    OSC_CMD_SINGLE = 0x05,      // SINGLE - 单次触发
    OSC_CMD_AUTO = 0x06,        // AUTO - 自动触发
    OSC_CMD_FORCE = 0x07,       // FORCE - 强制触发
    OSC_CMD_CLEAR = 0x08,       // CLEAR - 清除波形
    OSC_CMD_MEASURE = 0x09,     // MEASURE - 测量
    OSC_CMD_SET_INTERVAL = 0x0A, // 设置发送间隔
    OSC_CMD_AUTO_SEND = 0x0B,   // 启用/禁用自动发送
    OSC_CMD_DEBUG = 0x0C,       // 调试状态检查
    OSC_CMD_TEST_DATA = 0x0D,   // 强制填充测试数据
    OSC_CMD_REAL_ADC = 0x0E,    // 读取真实ADC数据
    OSC_CMD_SEND_WAVEFORM = 0x0F // 发送波形数据到上位机
} OscilloscopeCommand;
```

**优化后**：
```c
typedef enum {
    OSC_CMD_RUN_STOP = 0x01,    // RUN/STOP - 运行/停止
    OSC_CMD_SINGLE = 0x02,      // SINGLE - 单次触发
    OSC_CMD_AUTO = 0x03,        // AUTO - 自动触发
    OSC_CMD_FORCE = 0x04,       // FORCE - 强制触发
    OSC_CMD_CLEAR = 0x05,       // CLEAR - 清除波形
    OSC_CMD_MEASURE = 0x06      // MEASURE - 测量
} OscilloscopeCommand;
```

### 2. 命令处理逻辑优化 (`App/state_machine.c`)

#### 2.1 RUN/STOP命令合并
将原来的`OSC_CMD_START`和`OSC_CMD_STOP`合并为`OSC_CMD_RUN_STOP`，根据当前状态自动切换：
```c
case OSC_CMD_RUN_STOP:
    if (osc_config.state == OSC_STATE_RUN) {
        Oscilloscope_Stop();  // 当前运行中，执行停止
    } else {
        Oscilloscope_Start(); // 当前停止中，执行启动
    }
    break;
```

#### 2.2 命令处理简化
- 删除了复杂的调试和测试逻辑
- 每个命令都有明确的功能和反馈
- 添加了适当的错误处理和状态检查

#### 2.3 状态检查优化
修正了`StateMachine_HandleDataSending()`中的状态检查：
```c
// 优化前
if (osc_config.state == OSC_CMD_START) {

// 优化后  
if (osc_config.state == OSC_STATE_RUN) {
```

### 3. 功能保持完整性
虽然删除了调试命令，但核心功能保持完整：
- ✅ 波形采集和显示
- ✅ 触发检测和控制
- ✅ 测量功能（频率、峰峰值等）
- ✅ 数据发送到上位机
- ✅ 状态管理和切换

## 优化效果

### 1. 代码简洁性
- 命令定义从15个减少到6个，减少60%
- 删除了约100行不必要的调试代码
- 提高了代码的可读性和维护性

### 2. 系统架构一致性
- 严格按照文档要求实现功能
- 符合集中式状态机架构设计
- 保持了模块间的清晰接口

### 3. 功能稳定性
- 删除了可能引起混淆的调试命令
- 简化了命令处理逻辑，减少出错概率
- 保持了所有必需功能的完整性

## 测试建议
1. **基本功能测试**：验证6个命令的正确响应
2. **状态切换测试**：确认RUN/STOP命令的状态切换逻辑
3. **数据传输测试**：验证波形数据和测量结果的正确发送
4. **异常处理测试**：测试未知命令的错误处理

## 后续维护
1. 如需添加新功能，请严格按照文档要求和系统架构进行
2. 避免添加临时性的调试命令到正式代码中
3. 保持命令定义与文档的一致性

这次优化确保了代码的简洁性、一致性和可维护性，符合项目的整体架构设计理念。
