/*
 * USART.h
 * 串口模块化处理头文件
 * 串口 RX PA11
 * 串口 TX PA10
 */
#ifndef USART_H_
#define USART_H_

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

// 定义缓冲区大小
#define UART_RX_BUFFER_SIZE 256
#define UART_TX_BUFFER_SIZE 256

// 定义DMA接收缓冲区大小
#define UART_DMA_RX_BUFFER_SIZE 64
#define UART_PACKET_SIZE 32  // 每次DMA传输的字节数

// UART状态定义
typedef enum {
    UART_OK = 0,          // 操作成功
    UART_ERROR,           // 操作失败
    UART_BUSY,            // UART忙
    UART_TIMEOUT          // 操作超时
} UART_Status_t;

// UART配置结构体
typedef struct {
    uint32_t baudRate;    // 波特率
    bool enableRx;        // 使能接收
    bool enableTx;        // 使能发送
    bool enableInterrupt; // 使能中断
} UART_Config_t;

// UART默认配置 (921600, 8N1)
#define UART_DEFAULT_CONFIG { \
    .baudRate = 921600, \
    .enableRx = true, \
    .enableTx = true, \
    .enableInterrupt = true \
}

/**
 * @brief 初始化UART和DMA
 * 
 * @param config UART配置参数，传NULL则使用默认配置
 * @return UART_Status_t 初始化状态
 */
UART_Status_t UART_Init(UART_Config_t *config);

/**
 * @brief 配置DMA接收
 * 
 * @param rxBuffer 接收缓冲区
 * @param size 接收大小
 */
void UART_ConfigDmaRx(volatile uint8_t *rxBuffer, uint16_t size);

/**
 * @brief 发送单个字节
 * 
 * @param data 要发送的数据
 * @return UART_Status_t 发送状态
 */
UART_Status_t UART_SendByte(uint8_t data);

/**
 * @brief 发送字符串
 * 
 * @param str 要发送的字符串
 * @return UART_Status_t 发送状态
 */
UART_Status_t UART_SendString(const char *str);

/**
 * @brief 发送数据缓冲区
 * 
 * @param data 数据缓冲区
 * @param size 数据大小
 * @return UART_Status_t 发送状态
 */
UART_Status_t UART_SendBuffer(const uint8_t *data, uint16_t size);

/**
 * @brief 检查UART是否有数据可读
 * 
 * @return bool 有数据返回true，否则返回false
 */
bool UART_IsDataAvailable(void);

/**
 * @brief 获取接收到的字节
 * 
 * @param data 接收数据的指针
 * @return UART_Status_t 接收状态
 */
UART_Status_t UART_ReceiveByte(uint8_t *data);

/**
 * @brief 获取接收到的最新字节
 * 
 * @return uint8_t 接收到的字节
 */
uint8_t UART_GetLastReceivedByte(void);

/**
 * @brief 处理DMA接收到的数据
 * 当DMA完成接收后调用此函数处理接收到的数据
 */
void UART_ProcessReceivedData(void);

/**
 * @brief TJC协议解析数据处理
 * 处理所有可用的接收数据
 */
void UART_ProcessTJCData(void);

/**
 * @brief 获取UART调试统计信息
 */
void UART_GetDebugStats(void);

// 导出DMA接收相关变量
extern volatile uint8_t gRxPacket[UART_PACKET_SIZE];
extern volatile bool gRxComplete;

#endif /* USART_H_ */