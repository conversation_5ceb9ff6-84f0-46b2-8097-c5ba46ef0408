/**
 * @file state_machine.h
 * @brief 状态机处理模块头文件
 * @version 1.0
 * @date 2025-01-17
 * 
 * 本文件定义了状态机处理的接口和数据结构
 * 负责处理不同页面的命令分发和处理
 */

#ifndef __STATE_MACHINE_H__
#define __STATE_MACHINE_H__

#include <stdint.h>
#include <stdbool.h>
#include "tjc.h"



/* 状态机状态结构 */
typedef struct {
    uint8_t current_page_id;        // 当前页面ID
    bool new_command_received;      // 新命令接收标志
    TJC_Frame received_frame;       // 接收到的帧数据

    // 按键标志位
    bool key_pressed;               // 按键按下标志
    bool key_long_pressed;          // 长按标志
    bool key_released;              // 按键释放标志
} StateMachine_State;

/* 外部变量声明 */
extern StateMachine_State sm_state;
extern char uart_buffer[1000];

/* 主要接口函数 */

/**
 * @brief 初始化状态机
 */
void StateMachine_Init(void);

/**
 * @brief 主状态机处理函数
 * 根据当前页面ID分发命令到对应的页面处理函数
 */
void StateMachine_Process(void);

/**
 * @brief TJC协议帧接收回调函数
 * @param frame 接收到的帧数据
 */
void StateMachine_FrameReceivedCallback(const TJC_Frame* frame);

/**
 * @brief 设置当前页面ID
 * @param page_id 页面ID
 */
void StateMachine_SetCurrentPage(uint8_t page_id);

/**
 * @brief 获取当前页面ID
 * @return 当前页面ID
 */
uint8_t StateMachine_GetCurrentPage(void);

/**
 * @brief 检查是否有新命令需要处理
 * @return true: 有新命令, false: 无新命令
 */
bool StateMachine_HasNewCommand(void);

/* 页面处理函数 */

/**
 * @brief 主界面命令处理 - 只处理界面跳转
 */
void StateMachine_HandleMainPage(void);

/**
 * @brief 电机控制界面命令处理
 */
void StateMachine_HandleMotorPage(void);

/**
 * @brief 示波器界面命令处理
 */
void StateMachine_HandleOscilloscopePage(void);

/* 命令处理函数 */

/**
 * @brief 界面跳转命令处理
 * @param nav_cmd 跳转命令
 */
void StateMachine_HandleNavigation(uint8_t nav_cmd);

/**
 * @brief 电机控制命令处理
 * @param motor_cmd 电机命令类型
 */
void StateMachine_HandleMotorControl(uint8_t motor_cmd);

/**
 * @brief 示波器控制命令处理
 * @param osc_cmd 示波器命令类型
 * @param osc_value 示波器参数值
 */
void StateMachine_HandleOscilloscopeControl(uint8_t osc_cmd, uint8_t osc_value);

/* 数据发送处理函数 */

/**
 * @brief 各个界面数据发送处理
 * 根据当前页面ID发送对应的调试信息
 */
void StateMachine_HandleDataSending(void);

/**
 * @brief 设置按键按下标志
 */
void StateMachine_SetKeyPressed(void);

/**
 * @brief 设置按键长按标志
 */
void StateMachine_SetKeyLongPressed(void);

/**
 * @brief 设置按键释放标志
 */
void StateMachine_SetKeyReleased(void);

/**
 * @brief 处理按键事件（内部函数）
 */
void StateMachine_HandleKeyEvents(void);

/**
 * @brief 处理电机加速（内部函数）
 */
void StateMachine_HandleMotorSpeedIncrease(void);

/**
 * @brief 处理电机停止（内部函数）
 */
void StateMachine_HandleMotorStop(void);

#endif /* __STATE_MACHINE_H__ */
