/**
 * @file dsp_spectrum.h
 * @brief DSP频谱分析模块头文件
 * @version 1.0
 * @date 2025-01-29
 * 
 * 本文件定义了频谱分析的接口和数据结构
 * 提供频谱显示、峰值检测、频率测量等分析功能
 */

#ifndef __DSP_SPECTRUM_H__
#define __DSP_SPECTRUM_H__

#include "dsp_config.h"
#include "dsp_fft.h"

/* 频谱分析配置 */
typedef struct {
    uint16_t spectrum_length;               // 频谱长度
    uint32_t sample_rate;                   // 采样率
    DSP_Float frequency_resolution;         // 频率分辨率
    DSP_Float max_frequency;                // 最大频率
    bool enable_peak_detection;             // 启用峰值检测
    bool enable_frequency_measurement;      // 启用频率测量
    bool enable_power_calculation;          // 启用功率计算
    uint16_t peak_threshold_percent;        // 峰值阈值百分比 (0-100)
    uint16_t max_peaks;                     // 最大峰值数量
} DSP_Spectrum_Config;

/* 峰值信息结构 */
typedef struct {
    uint16_t index;                         // 峰值索引
    DSP_Float frequency;                    // 峰值频率
    DSP_Float magnitude;                    // 峰值幅度
    DSP_Float magnitude_db;                 // 峰值幅度(dB)
    DSP_Float power;                        // 峰值功率
} DSP_Peak_Info;

/* 频谱分析结果结构 */
typedef struct {
    DSP_Float *magnitude_spectrum;          // 幅度谱
    DSP_Float *power_spectrum;              // 功率谱
    DSP_Float *magnitude_db;                // dB幅度谱
    DSP_Peak_Info *peaks;                   // 峰值信息数组
    uint16_t peak_count;                    // 检测到的峰值数量
    DSP_Float total_power;                  // 总功率
    DSP_Float fundamental_frequency;        // 基频
    DSP_Float dominant_frequency;           // 主频
    DSP_Float frequency_centroid;           // 频率重心
    DSP_Float spectral_rolloff;             // 频谱滚降点
    bool valid;                             // 结果有效标志
} DSP_Spectrum_Result;

/* 频谱分析句柄 */
typedef struct {
    DSP_FFT_Handle *fft_handle;             // FFT句柄指针
    DSP_Spectrum_Config config;             // 配置参数
    DSP_Spectrum_Result result;             // 分析结果
    DSP_Float *temp_buffer;                 // 临时缓冲区
    bool initialized;                       // 初始化标志
} DSP_Spectrum_Handle;

/* 频谱分析初始化和管理函数 */

/**
 * @brief 初始化频谱分析模块
 * @param handle 频谱分析句柄指针
 * @param fft_handle FFT句柄指针
 * @param config 配置参数指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_Init(DSP_Spectrum_Handle *handle, 
                             DSP_FFT_Handle *fft_handle,
                             const DSP_Spectrum_Config *config);

/**
 * @brief 反初始化频谱分析模块
 * @param handle 频谱分析句柄指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_DeInit(DSP_Spectrum_Handle *handle);

/**
 * @brief 重置频谱分析模块
 * @param handle 频谱分析句柄指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_Reset(DSP_Spectrum_Handle *handle);

/* 频谱分析函数 */

/**
 * @brief 执行频谱分析
 * @param handle 频谱分析句柄指针
 * @param input_data 输入数据指针
 * @param result 分析结果指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_Analyze(DSP_Spectrum_Handle *handle, 
                                const DSP_Float *input_data,
                                DSP_Spectrum_Result *result);

/**
 * @brief 计算功率谱密度
 * @param handle 频谱分析句柄指针
 * @param magnitude_spectrum 幅度谱指针
 * @param power_spectrum 功率谱输出指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_ComputePowerSpectrum(DSP_Spectrum_Handle *handle,
                                             const DSP_Float *magnitude_spectrum,
                                             DSP_Float *power_spectrum);

/**
 * @brief 计算dB幅度谱
 * @param magnitude_spectrum 幅度谱指针
 * @param magnitude_db dB幅度谱输出指针
 * @param length 数据长度
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_ComputeMagnitudeDb(const DSP_Float *magnitude_spectrum,
                                           DSP_Float *magnitude_db,
                                           uint16_t length);

/* 峰值检测函数 */

/**
 * @brief 检测频谱峰值
 * @param handle 频谱分析句柄指针
 * @param magnitude_spectrum 幅度谱指针
 * @param peaks 峰值信息数组指针
 * @param max_peaks 最大峰值数量
 * @param found_peaks 找到的峰值数量指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_DetectPeaks(DSP_Spectrum_Handle *handle,
                                    const DSP_Float *magnitude_spectrum,
                                    DSP_Peak_Info *peaks,
                                    uint16_t max_peaks,
                                    uint16_t *found_peaks);

/**
 * @brief 查找主频
 * @param peaks 峰值信息数组指针
 * @param peak_count 峰值数量
 * @param dominant_frequency 主频输出指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_FindDominantFrequency(const DSP_Peak_Info *peaks,
                                              uint16_t peak_count,
                                              DSP_Float *dominant_frequency);

/**
 * @brief 查找基频
 * @param peaks 峰值信息数组指针
 * @param peak_count 峰值数量
 * @param fundamental_frequency 基频输出指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_FindFundamentalFrequency(const DSP_Peak_Info *peaks,
                                                 uint16_t peak_count,
                                                 DSP_Float *fundamental_frequency);

/* 频谱特征计算函数 */

/**
 * @brief 计算频率重心
 * @param magnitude_spectrum 幅度谱指针
 * @param length 数据长度
 * @param sample_rate 采样率
 * @param centroid 频率重心输出指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_ComputeCentroid(const DSP_Float *magnitude_spectrum,
                                        uint16_t length,
                                        uint32_t sample_rate,
                                        DSP_Float *centroid);

/**
 * @brief 计算频谱滚降点
 * @param magnitude_spectrum 幅度谱指针
 * @param length 数据长度
 * @param sample_rate 采样率
 * @param rolloff_percent 滚降百分比 (0-100)
 * @param rolloff_frequency 滚降频率输出指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_ComputeRolloff(const DSP_Float *magnitude_spectrum,
                                       uint16_t length,
                                       uint32_t sample_rate,
                                       DSP_Float rolloff_percent,
                                       DSP_Float *rolloff_frequency);

/**
 * @brief 计算总功率
 * @param power_spectrum 功率谱指针
 * @param length 数据长度
 * @param total_power 总功率输出指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_ComputeTotalPower(const DSP_Float *power_spectrum,
                                          uint16_t length,
                                          DSP_Float *total_power);

/* 频谱显示辅助函数 */

/**
 * @brief 对数频率轴映射
 * @param linear_spectrum 线性频谱指针
 * @param linear_length 线性频谱长度
 * @param log_spectrum 对数频谱输出指针
 * @param log_length 对数频谱长度
 * @param sample_rate 采样率
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_LinearToLogScale(const DSP_Float *linear_spectrum,
                                         uint16_t linear_length,
                                         DSP_Float *log_spectrum,
                                         uint16_t log_length,
                                         uint32_t sample_rate);

/**
 * @brief 频谱平滑处理
 * @param spectrum 频谱数据指针
 * @param length 数据长度
 * @param smooth_factor 平滑因子 (0.0-1.0)
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_Spectrum_Smooth(DSP_Float *spectrum,
                               uint16_t length,
                               DSP_Float smooth_factor);

/* 预定义的频谱分析配置 */
extern const DSP_Spectrum_Config DSP_SPECTRUM_CONFIG_DEFAULT;
extern const DSP_Spectrum_Config DSP_SPECTRUM_CONFIG_AUDIO;
extern const DSP_Spectrum_Config DSP_SPECTRUM_CONFIG_SIGNAL_ANALYSIS;

/* 全局频谱分析句柄 */
extern DSP_Spectrum_Handle g_spectrum_handle;

#endif /* __DSP_SPECTRUM_H__ */
