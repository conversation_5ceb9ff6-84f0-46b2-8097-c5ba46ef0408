/**
 * @file dsp_fft.c
 * @brief DSP FFT模块实现
 * @version 1.0
 * @date 2025-01-29
 */

#include "dsp_fft.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>

/* 全局FFT句柄 */
DSP_FFT_Handle g_fft_handle = {0};

/* 预定义的FFT配置 */
const DSP_FFT_Config DSP_FFT_CONFIG_DEFAULT = {
    .fft_size = DSP_FFT_SIZE_512,
    .sample_rate = DSP_SAMPLE_RATE_16K,
    .window_type = DSP_WINDOW_HANNING,
    .use_window = true,
    .compute_magnitude = true,
    .compute_phase = false
};

const DSP_FFT_Config DSP_FFT_CONFIG_AUDIO_16K = {
    .fft_size = DSP_FFT_SIZE_512,
    .sample_rate = DSP_SAMPLE_RATE_16K,
    .window_type = DSP_WINDOW_HANNING,
    .use_window = true,
    .compute_magnitude = true,
    .compute_phase = true
};

const DSP_FFT_Config DSP_FFT_CONFIG_AUDIO_48K = {
    .fft_size = DSP_FFT_SIZE_1024,
    .sample_rate = DSP_SAMPLE_RATE_48K,
    .window_type = DSP_WINDOW_BLACKMAN,
    .use_window = true,
    .compute_magnitude = true,
    .compute_phase = true
};

/* 私有函数声明 */
static DSP_Status DSP_FFT_AllocateBuffers(DSP_FFT_Handle *handle);
static DSP_Status DSP_FFT_FreeBuffers(DSP_FFT_Handle *handle);
static DSP_Status DSP_FFT_ValidateHandle(DSP_FFT_Handle *handle);
static DSP_Status DSP_FFT_ValidateConfig(const DSP_FFT_Config *config);

/**
 * @brief 初始化FFT模块
 */
DSP_Status DSP_FFT_Init(DSP_FFT_Handle *handle, const DSP_FFT_Config *config)
{
    DSP_ASSERT(handle != NULL);
    DSP_ASSERT(config != NULL);
    
    DSP_DEBUG("Initializing FFT module: size=%u, sample_rate=%lu", 
              config->fft_size, config->sample_rate);
    
    /* 验证配置参数 */
    DSP_Status status = DSP_FFT_ValidateConfig(config);
    if (status != DSP_STATUS_OK) {
        DSP_ERROR("Invalid FFT configuration");
        return status;
    }
    
    /* 清零句柄 */
    memset(handle, 0, sizeof(DSP_FFT_Handle));
    
    /* 设置基本参数 */
    handle->fft_size = config->fft_size;
    handle->window_type = config->window_type;
    handle->initialized = false;
    
    /* 分配缓冲区 */
    status = DSP_FFT_AllocateBuffers(handle);
    if (status != DSP_STATUS_OK) {
        DSP_ERROR("Failed to allocate FFT buffers");
        return status;
    }
    
    /* 初始化实数FFT实例 */
    arm_status arm_status = arm_rfft_fast_init_f32(&handle->rfft_instance, handle->fft_size);
    if (arm_status != ARM_MATH_SUCCESS) {
        DSP_ERROR("Failed to initialize real FFT instance: %d", arm_status);
        DSP_FFT_FreeBuffers(handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 生成窗函数 */
    if (config->use_window) {
        status = DSP_FFT_GenerateWindow(config->window_type, handle->window_buffer, handle->fft_size);
        if (status != DSP_STATUS_OK) {
            DSP_ERROR("Failed to generate window function");
            DSP_FFT_FreeBuffers(handle);
            return status;
        }
    }
    
    handle->initialized = true;
    DSP_DEBUG("FFT module initialized successfully");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 反初始化FFT模块
 */
DSP_Status DSP_FFT_DeInit(DSP_FFT_Handle *handle)
{
    DSP_Status status = DSP_FFT_ValidateHandle(handle);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    DSP_DEBUG("De-initializing FFT module");
    
    /* 释放缓冲区 */
    DSP_FFT_FreeBuffers(handle);
    
    /* 清零句柄 */
    memset(handle, 0, sizeof(DSP_FFT_Handle));
    
    DSP_DEBUG("FFT module de-initialized");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 重置FFT模块
 */
DSP_Status DSP_FFT_Reset(DSP_FFT_Handle *handle)
{
    DSP_Status status = DSP_FFT_ValidateHandle(handle);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    DSP_DEBUG("Resetting FFT module");
    
    /* 清零缓冲区 */
    if (handle->input_buffer) {
        memset(handle->input_buffer, 0, handle->fft_size * sizeof(DSP_Float));
    }
    if (handle->output_buffer) {
        memset(handle->output_buffer, 0, handle->fft_size * 2 * sizeof(DSP_Float));
    }
    if (handle->magnitude_buffer) {
        memset(handle->magnitude_buffer, 0, (handle->fft_size / 2) * sizeof(DSP_Float));
    }
    if (handle->phase_buffer) {
        memset(handle->phase_buffer, 0, (handle->fft_size / 2) * sizeof(DSP_Float));
    }
    
    DSP_DEBUG("FFT module reset completed");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 执行实数FFT变换
 */
DSP_Status DSP_FFT_Compute(DSP_FFT_Handle *handle, const DSP_Float *input, DSP_FFT_Result *result)
{
    DSP_ASSERT(handle != NULL);
    DSP_ASSERT(input != NULL);
    DSP_ASSERT(result != NULL);
    
    DSP_Status status = DSP_FFT_ValidateHandle(handle);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    DSP_PERF_START();
    
    /* 复制输入数据到内部缓冲区 */
    memcpy(handle->input_buffer, input, handle->fft_size * sizeof(DSP_Float));
    
    /* 应用窗函数（如果启用） */
    if (handle->window_buffer != NULL) {
        arm_mult_f32(handle->input_buffer, handle->window_buffer, 
                     handle->input_buffer, handle->fft_size);
    }
    
    /* 执行实数FFT */
    arm_rfft_fast_f32(&handle->rfft_instance, handle->input_buffer, handle->output_buffer, 0);
    
    /* 计算幅度谱 */
    arm_cmplx_mag_f32(handle->output_buffer, handle->magnitude_buffer, handle->fft_size / 2);
    
    /* 计算相位谱（如果需要） */
    if (handle->phase_buffer != NULL) {
        for (uint16_t i = 0; i < handle->fft_size / 2; i++) {
            DSP_Float real = handle->output_buffer[2 * i];
            DSP_Float imag = handle->output_buffer[2 * i + 1];
            handle->phase_buffer[i] = atan2f(imag, real);
        }
    }
    
    /* 设置结果 */
    result->magnitude = handle->magnitude_buffer;
    result->phase = handle->phase_buffer;
    result->complex_output = handle->output_buffer;
    result->spectrum_length = handle->fft_size / 2;
    result->valid = true;
    
    DSP_PERF_END("FFT_Compute");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 查找频谱峰值
 */
DSP_Status DSP_FFT_FindPeak(const DSP_Float *magnitude, 
                            uint16_t length, 
                            uint16_t *peak_index, 
                            DSP_Float *peak_value)
{
    DSP_ASSERT(magnitude != NULL);
    DSP_ASSERT(peak_index != NULL);
    DSP_ASSERT(peak_value != NULL);
    DSP_ASSERT(length > 0);
    
    uint32_t max_index;
    DSP_Float max_value;
    
    /* 使用CMSIS-DSP库查找最大值 */
    arm_max_f32(magnitude, length, &max_value, &max_index);
    
    *peak_index = (uint16_t)max_index;
    *peak_value = max_value;
    
    DSP_DEBUG("Peak found: index=%u, value=%.3f", *peak_index, *peak_value);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 生成窗函数系数
 */
DSP_Status DSP_FFT_GenerateWindow(DSP_WindowType window_type, 
                                  DSP_Float *coeffs, 
                                  uint16_t length)
{
    DSP_ASSERT(coeffs != NULL);
    DSP_ASSERT(length > 0);
    
    DSP_DEBUG("Generating window function: type=%d, length=%u", window_type, length);
    
    switch (window_type) {
        case DSP_WINDOW_RECTANGULAR:
            for (uint16_t i = 0; i < length; i++) {
                coeffs[i] = 1.0f;
            }
            break;
            
        case DSP_WINDOW_HANNING:
            for (uint16_t i = 0; i < length; i++) {
                coeffs[i] = 0.5f * (1.0f - cosf(TWO_PI * i / (length - 1)));
            }
            break;
            
        case DSP_WINDOW_HAMMING:
            for (uint16_t i = 0; i < length; i++) {
                coeffs[i] = 0.54f - 0.46f * cosf(TWO_PI * i / (length - 1));
            }
            break;
            
        case DSP_WINDOW_BLACKMAN:
            for (uint16_t i = 0; i < length; i++) {
                DSP_Float n = (DSP_Float)i / (length - 1);
                coeffs[i] = 0.42f - 0.5f * cosf(TWO_PI * n) + 0.08f * cosf(4.0f * PI * n);
            }
            break;
            
        default:
            DSP_ERROR("Unsupported window type: %d", window_type);
            return DSP_STATUS_INVALID_PARAM;
    }
    
    DSP_DEBUG("Window function generated successfully");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 获取FFT输出的频率分辨率
 */
DSP_Float DSP_FFT_GetFrequencyResolution(DSP_FFT_Handle *handle, uint32_t sample_rate)
{
    if (handle == NULL || !handle->initialized) {
        return 0.0f;
    }
    
    return (DSP_Float)sample_rate / handle->fft_size;
}

/**
 * @brief 将FFT索引转换为频率
 */
DSP_Float DSP_FFT_IndexToFrequency(uint16_t index, uint32_t sample_rate, uint16_t fft_size)
{
    return (DSP_Float)index * sample_rate / fft_size;
}

/**
 * @brief 将频率转换为FFT索引
 */
uint16_t DSP_FFT_FrequencyToIndex(DSP_Float frequency, uint32_t sample_rate, uint16_t fft_size)
{
    uint16_t index = (uint16_t)(frequency * fft_size / sample_rate + 0.5f);
    
    /* 限制在有效范围内 */
    if (index >= fft_size / 2) {
        index = fft_size / 2 - 1;
    }
    
    return index;
}

/**
 * @brief 计算dB值
 */
DSP_Float DSP_FFT_MagnitudeToDb(DSP_Float magnitude)
{
    if (magnitude <= 0.0f) {
        return -120.0f;  // 最小dB值
    }

    return 20.0f * log10f(magnitude);
}

/**
 * @brief 归一化幅度谱
 */
DSP_Status DSP_FFT_NormalizeMagnitude(DSP_Float *magnitude,
                                      uint16_t length,
                                      DSP_Float *max_value)
{
    DSP_ASSERT(magnitude != NULL);
    DSP_ASSERT(length > 0);

    /* 查找最大值 */
    uint32_t max_index;
    DSP_Float max_val;
    arm_max_f32(magnitude, length, &max_val, &max_index);

    if (max_val == 0.0f) {
        DSP_ERROR("Cannot normalize zero magnitude spectrum");
        return DSP_STATUS_ERROR;
    }

    /* 归一化 */
    DSP_Float scale = 1.0f / max_val;
    arm_scale_f32(magnitude, scale, magnitude, length);

    if (max_value != NULL) {
        *max_value = max_val;
    }

    DSP_DEBUG("Magnitude spectrum normalized: max_value=%.3f", max_val);

    return DSP_STATUS_OK;
}

/* 私有函数实现 */

/**
 * @brief 分配FFT缓冲区
 */
static DSP_Status DSP_FFT_AllocateBuffers(DSP_FFT_Handle *handle)
{
    if (handle == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    DSP_DEBUG("Allocating FFT buffers: fft_size=%u", handle->fft_size);

    /* 分配输入缓冲区 */
    handle->input_buffer = (DSP_Float*)DSP_MALLOC(handle->fft_size * sizeof(DSP_Float));
    if (handle->input_buffer == NULL) {
        DSP_ERROR("Failed to allocate FFT input buffer");
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    /* 分配输出缓冲区（复数，需要2倍空间） */
    handle->output_buffer = (DSP_Float*)DSP_MALLOC(handle->fft_size * 2 * sizeof(DSP_Float));
    if (handle->output_buffer == NULL) {
        DSP_ERROR("Failed to allocate FFT output buffer");
        DSP_FFT_FreeBuffers(handle);
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    /* 分配幅度谱缓冲区 */
    handle->magnitude_buffer = (DSP_Float*)DSP_MALLOC((handle->fft_size / 2) * sizeof(DSP_Float));
    if (handle->magnitude_buffer == NULL) {
        DSP_ERROR("Failed to allocate magnitude buffer");
        DSP_FFT_FreeBuffers(handle);
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    /* 分配相位谱缓冲区 */
    handle->phase_buffer = (DSP_Float*)DSP_MALLOC((handle->fft_size / 2) * sizeof(DSP_Float));
    if (handle->phase_buffer == NULL) {
        DSP_ERROR("Failed to allocate phase buffer");
        DSP_FFT_FreeBuffers(handle);
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    /* 分配窗函数缓冲区 */
    handle->window_buffer = (DSP_Float*)DSP_MALLOC(handle->fft_size * sizeof(DSP_Float));
    if (handle->window_buffer == NULL) {
        DSP_ERROR("Failed to allocate window buffer");
        DSP_FFT_FreeBuffers(handle);
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    DSP_DEBUG("All FFT buffers allocated successfully");

    return DSP_STATUS_OK;
}

/**
 * @brief 释放FFT缓冲区
 */
static DSP_Status DSP_FFT_FreeBuffers(DSP_FFT_Handle *handle)
{
    if (handle == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    DSP_DEBUG("Freeing FFT buffers");

    if (handle->input_buffer) {
        DSP_FREE(handle->input_buffer);
        handle->input_buffer = NULL;
    }

    if (handle->output_buffer) {
        DSP_FREE(handle->output_buffer);
        handle->output_buffer = NULL;
    }

    if (handle->magnitude_buffer) {
        DSP_FREE(handle->magnitude_buffer);
        handle->magnitude_buffer = NULL;
    }

    if (handle->phase_buffer) {
        DSP_FREE(handle->phase_buffer);
        handle->phase_buffer = NULL;
    }

    if (handle->window_buffer) {
        DSP_FREE(handle->window_buffer);
        handle->window_buffer = NULL;
    }

    DSP_DEBUG("All FFT buffers freed");

    return DSP_STATUS_OK;
}

/**
 * @brief 验证FFT句柄有效性
 */
static DSP_Status DSP_FFT_ValidateHandle(DSP_FFT_Handle *handle)
{
    if (handle == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    if (!handle->initialized) {
        return DSP_STATUS_NOT_INITIALIZED;
    }

    return DSP_STATUS_OK;
}

/**
 * @brief 验证FFT配置有效性
 */
static DSP_Status DSP_FFT_ValidateConfig(const DSP_FFT_Config *config)
{
    if (config == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    /* 检查FFT大小 */
    if (!DSP_IsValidFFTSize(config->fft_size)) {
        DSP_ERROR("Invalid FFT size: %u", config->fft_size);
        return DSP_STATUS_INVALID_PARAM;
    }

    /* 检查采样率 */
    if (config->sample_rate == 0) {
        DSP_ERROR("Invalid sample rate: %lu", config->sample_rate);
        return DSP_STATUS_INVALID_PARAM;
    }

    /* 检查窗函数类型 */
    if (config->window_type >= DSP_WINDOW_KAISER + 1) {
        DSP_ERROR("Invalid window type: %d", config->window_type);
        return DSP_STATUS_INVALID_PARAM;
    }

    return DSP_STATUS_OK;
}
