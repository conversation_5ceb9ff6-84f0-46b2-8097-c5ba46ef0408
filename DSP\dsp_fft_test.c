/**
 * @file dsp_fft_test.c
 * @brief DSP FFT模块测试文件
 * @version 1.0
 * @date 2025-01-29
 */

#include "dsp_fft.h"
#include <stdio.h>
#include <math.h>

/* 测试用的全局变量 */
static DSP_FFT_Handle test_fft_handle;
static DSP_Float test_signal[512];
static DSP_FFT_Result test_result;

/* 测试函数声明 */
static DSP_Status DSP_FFT_Test_BasicInit(void);
static DSP_Status DSP_FFT_Test_WindowFunction(void);
static DSP_Status DSP_FFT_Test_SineWaveFFT(void);
static DSP_Status DSP_FFT_Test_PeakDetection(void);
static DSP_Status DSP_FFT_Test_FrequencyConversion(void);
static void DSP_FFT_Test_GenerateSineWave(DSP_Float *buffer, uint16_t length, 
                                          DSP_Float frequency, DSP_Float sample_rate, 
                                          DSP_Float amplitude);

/**
 * @brief FFT模块完整测试
 * @return DSP_Status 测试结果
 */
DSP_Status DSP_FFT_RunAllTests(void)
{
    DSP_Status status;
    uint32_t passed_tests = 0;
    uint32_t total_tests = 5;
    
    printf("\r\n=== DSP FFT Module Test Suite ===\r\n");
    
    /* 测试1: 基本初始化 */
    printf("Test 1: FFT Basic Initialization... ");
    status = DSP_FFT_Test_BasicInit();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试2: 窗函数 */
    printf("Test 2: FFT Window Function... ");
    status = DSP_FFT_Test_WindowFunction();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试3: 正弦波FFT */
    printf("Test 3: Sine Wave FFT... ");
    status = DSP_FFT_Test_SineWaveFFT();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试4: 峰值检测 */
    printf("Test 4: Peak Detection... ");
    status = DSP_FFT_Test_PeakDetection();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试5: 频率转换 */
    printf("Test 5: Frequency Conversion... ");
    status = DSP_FFT_Test_FrequencyConversion();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 输出测试结果 */
    printf("\r\n=== FFT Test Results ===\r\n");
    printf("Passed: %lu/%lu tests\r\n", passed_tests, total_tests);
    
    if (passed_tests == total_tests) {
        printf("All FFT tests PASSED! FFT module is ready.\r\n");
        return DSP_STATUS_OK;
    } else {
        printf("Some FFT tests FAILED! Please check FFT implementation.\r\n");
        return DSP_STATUS_ERROR;
    }
}

/**
 * @brief 测试FFT基本初始化功能
 */
static DSP_Status DSP_FFT_Test_BasicInit(void)
{
    DSP_Status status;
    
    /* 使用默认配置初始化 */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    /* 检查初始化状态 */
    if (!test_fft_handle.initialized ||
        test_fft_handle.fft_size != DSP_FFT_SIZE_512 ||
        test_fft_handle.input_buffer == NULL ||
        test_fft_handle.output_buffer == NULL ||
        test_fft_handle.magnitude_buffer == NULL) {
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 测试重置功能 */
    status = DSP_FFT_Reset(&test_fft_handle);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 清理 */
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试窗函数功能
 */
static DSP_Status DSP_FFT_Test_WindowFunction(void)
{
    DSP_Status status;
    DSP_Float window_coeffs[256];
    
    /* 测试不同窗函数类型 */
    DSP_WindowType window_types[] = {
        DSP_WINDOW_RECTANGULAR,
        DSP_WINDOW_HANNING,
        DSP_WINDOW_HAMMING,
        DSP_WINDOW_BLACKMAN
    };
    
    for (int i = 0; i < 4; i++) {
        status = DSP_FFT_GenerateWindow(window_types[i], window_coeffs, 256);
        if (status != DSP_STATUS_OK) {
            return status;
        }
        
        /* 检查窗函数系数是否合理 */
        for (int j = 0; j < 256; j++) {
            if (window_coeffs[j] < 0.0f || window_coeffs[j] > 1.0f) {
                return DSP_STATUS_ERROR;
            }
        }
        
        /* 检查窗函数对称性（除了矩形窗） */
        if (window_types[i] != DSP_WINDOW_RECTANGULAR) {
            if (fabsf(window_coeffs[0] - window_coeffs[255]) > 0.001f ||
                fabsf(window_coeffs[127] - window_coeffs[128]) > 0.001f) {
                return DSP_STATUS_ERROR;
            }
        }
    }
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试正弦波FFT功能
 */
static DSP_Status DSP_FFT_Test_SineWaveFFT(void)
{
    DSP_Status status;
    
    /* 初始化FFT */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    /* 生成1kHz正弦波 */
    DSP_Float test_frequency = 1000.0f;
    DSP_Float sample_rate = DSP_SAMPLE_RATE_16K;
    DSP_FFT_Test_GenerateSineWave(test_signal, DSP_FFT_SIZE_512, 
                                  test_frequency, sample_rate, 1.0f);
    
    /* 执行FFT */
    status = DSP_FFT_Compute(&test_fft_handle, test_signal, &test_result);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 验证结果 */
    if (!test_result.valid ||
        test_result.magnitude == NULL ||
        test_result.spectrum_length != DSP_FFT_SIZE_512 / 2) {
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 查找峰值 */
    uint16_t peak_index;
    DSP_Float peak_value;
    status = DSP_FFT_FindPeak(test_result.magnitude, test_result.spectrum_length, 
                             &peak_index, &peak_value);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 验证峰值频率 */
    DSP_Float detected_frequency = DSP_FFT_IndexToFrequency(peak_index, sample_rate, DSP_FFT_SIZE_512);
    DSP_Float frequency_error = fabsf(detected_frequency - test_frequency);
    DSP_Float frequency_resolution = DSP_FFT_GetFrequencyResolution(&test_fft_handle, sample_rate);
    
    if (frequency_error > frequency_resolution) {
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 清理 */
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试峰值检测功能
 */
static DSP_Status DSP_FFT_Test_PeakDetection(void)
{
    DSP_Status status;
    
    /* 初始化FFT */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    /* 生成多频率信号：500Hz + 2000Hz */
    DSP_Float sample_rate = DSP_SAMPLE_RATE_16K;
    memset(test_signal, 0, sizeof(test_signal));
    
    DSP_Float temp_signal[512];
    DSP_FFT_Test_GenerateSineWave(temp_signal, DSP_FFT_SIZE_512, 500.0f, sample_rate, 0.8f);
    for (int i = 0; i < DSP_FFT_SIZE_512; i++) {
        test_signal[i] += temp_signal[i];
    }
    
    DSP_FFT_Test_GenerateSineWave(temp_signal, DSP_FFT_SIZE_512, 2000.0f, sample_rate, 0.6f);
    for (int i = 0; i < DSP_FFT_SIZE_512; i++) {
        test_signal[i] += temp_signal[i];
    }
    
    /* 执行FFT */
    status = DSP_FFT_Compute(&test_fft_handle, test_signal, &test_result);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 查找主峰值 */
    uint16_t peak_index;
    DSP_Float peak_value;
    status = DSP_FFT_FindPeak(test_result.magnitude, test_result.spectrum_length, 
                             &peak_index, &peak_value);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 验证峰值合理性 */
    if (peak_value <= 0.0f || peak_index >= test_result.spectrum_length) {
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 清理 */
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试频率转换功能
 */
static DSP_Status DSP_FFT_Test_FrequencyConversion(void)
{
    uint32_t sample_rate = DSP_SAMPLE_RATE_16K;
    uint16_t fft_size = DSP_FFT_SIZE_512;
    
    /* 测试频率分辨率 */
    DSP_Float expected_resolution = (DSP_Float)sample_rate / fft_size;
    
    /* 测试频率索引转换 */
    DSP_Float test_frequencies[] = {100.0f, 1000.0f, 5000.0f, 7000.0f};
    
    for (int i = 0; i < 4; i++) {
        DSP_Float freq = test_frequencies[i];
        uint16_t index = DSP_FFT_FrequencyToIndex(freq, sample_rate, fft_size);
        DSP_Float converted_freq = DSP_FFT_IndexToFrequency(index, sample_rate, fft_size);
        
        DSP_Float error = fabsf(converted_freq - freq);
        if (error > expected_resolution) {
            return DSP_STATUS_ERROR;
        }
    }
    
    /* 测试dB转换 */
    DSP_Float test_magnitudes[] = {1.0f, 0.5f, 0.1f, 0.01f};
    for (int i = 0; i < 4; i++) {
        DSP_Float db_value = DSP_FFT_MagnitudeToDb(test_magnitudes[i]);
        if (db_value > 0.0f || db_value < -120.0f) {
            return DSP_STATUS_ERROR;
        }
    }
    
    return DSP_STATUS_OK;
}

/**
 * @brief 生成正弦波测试信号
 */
static void DSP_FFT_Test_GenerateSineWave(DSP_Float *buffer, uint16_t length, 
                                          DSP_Float frequency, DSP_Float sample_rate, 
                                          DSP_Float amplitude)
{
    for (uint16_t i = 0; i < length; i++) {
        DSP_Float t = (DSP_Float)i / sample_rate;
        buffer[i] = amplitude * sinf(TWO_PI * frequency * t);
    }
}
