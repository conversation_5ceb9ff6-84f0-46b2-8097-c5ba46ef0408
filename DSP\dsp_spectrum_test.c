/**
 * @file dsp_spectrum_test.c
 * @brief DSP频谱分析模块测试文件
 * @version 1.0
 * @date 2025-01-29
 */

#include "dsp_spectrum.h"
#include <stdio.h>
#include <math.h>

/* 测试用的全局变量 */
static DSP_FFT_Handle test_fft_handle;
static DSP_Spectrum_Handle test_spectrum_handle;
static DSP_Float test_signal[512];
static DSP_Spectrum_Result test_result;

/* 测试函数声明 */
static DSP_Status DSP_Spectrum_Test_BasicInit(void);
static DSP_Status DSP_Spectrum_Test_SpectrumAnalysis(void);
static DSP_Status DSP_Spectrum_Test_PeakDetection(void);
static DSP_Status DSP_Spectrum_Test_FrequencyMeasurement(void);
static DSP_Status DSP_Spectrum_Test_PowerCalculation(void);
static void DSP_Spectrum_Test_GenerateTestSignal(DSP_Float *buffer, uint16_t length);

/**
 * @brief 频谱分析模块完整测试
 * @return DSP_Status 测试结果
 */
DSP_Status DSP_Spectrum_RunAllTests(void)
{
    DSP_Status status;
    uint32_t passed_tests = 0;
    uint32_t total_tests = 5;
    
    printf("\r\n=== DSP Spectrum Analysis Module Test Suite ===\r\n");
    
    /* 测试1: 基本初始化 */
    printf("Test 1: Spectrum Basic Initialization... ");
    status = DSP_Spectrum_Test_BasicInit();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试2: 频谱分析 */
    printf("Test 2: Spectrum Analysis... ");
    status = DSP_Spectrum_Test_SpectrumAnalysis();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试3: 峰值检测 */
    printf("Test 3: Peak Detection... ");
    status = DSP_Spectrum_Test_PeakDetection();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试4: 频率测量 */
    printf("Test 4: Frequency Measurement... ");
    status = DSP_Spectrum_Test_FrequencyMeasurement();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 测试5: 功率计算 */
    printf("Test 5: Power Calculation... ");
    status = DSP_Spectrum_Test_PowerCalculation();
    if (status == DSP_STATUS_OK) {
        printf("PASSED\r\n");
        passed_tests++;
    } else {
        printf("FAILED (status=%d)\r\n", status);
    }
    
    /* 输出测试结果 */
    printf("\r\n=== Spectrum Analysis Test Results ===\r\n");
    printf("Passed: %lu/%lu tests\r\n", passed_tests, total_tests);
    
    if (passed_tests == total_tests) {
        printf("All spectrum analysis tests PASSED! Module is ready.\r\n");
        return DSP_STATUS_OK;
    } else {
        printf("Some spectrum analysis tests FAILED! Please check implementation.\r\n");
        return DSP_STATUS_ERROR;
    }
}

/**
 * @brief 测试频谱分析基本初始化功能
 */
static DSP_Status DSP_Spectrum_Test_BasicInit(void)
{
    DSP_Status status;
    
    /* 初始化FFT */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    /* 初始化频谱分析 */
    status = DSP_Spectrum_Init(&test_spectrum_handle, &test_fft_handle, &DSP_SPECTRUM_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 检查初始化状态 */
    if (!test_spectrum_handle.initialized ||
        test_spectrum_handle.fft_handle == NULL ||
        test_spectrum_handle.result.magnitude_spectrum == NULL) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 清理 */
    DSP_Spectrum_DeInit(&test_spectrum_handle);
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试频谱分析功能
 */
static DSP_Status DSP_Spectrum_Test_SpectrumAnalysis(void)
{
    DSP_Status status;
    
    /* 初始化 */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    status = DSP_Spectrum_Init(&test_spectrum_handle, &test_fft_handle, &DSP_SPECTRUM_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 生成测试信号 */
    DSP_Spectrum_Test_GenerateTestSignal(test_signal, DSP_FFT_SIZE_512);
    
    /* 执行频谱分析 */
    status = DSP_Spectrum_Analyze(&test_spectrum_handle, test_signal, &test_result);
    if (status != DSP_STATUS_OK) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 验证结果 */
    if (!test_result.valid ||
        test_result.magnitude_spectrum == NULL ||
        test_result.magnitude_db == NULL) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 清理 */
    DSP_Spectrum_DeInit(&test_spectrum_handle);
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试峰值检测功能
 */
static DSP_Status DSP_Spectrum_Test_PeakDetection(void)
{
    DSP_Status status;
    
    /* 初始化 */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    status = DSP_Spectrum_Init(&test_spectrum_handle, &test_fft_handle, &DSP_SPECTRUM_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 生成包含明显峰值的测试信号 */
    DSP_Spectrum_Test_GenerateTestSignal(test_signal, DSP_FFT_SIZE_512);
    
    /* 执行频谱分析 */
    status = DSP_Spectrum_Analyze(&test_spectrum_handle, test_signal, &test_result);
    if (status != DSP_STATUS_OK) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 验证峰值检测结果 */
    if (test_result.peak_count == 0 ||
        test_result.peaks == NULL ||
        test_result.dominant_frequency <= 0.0f) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 清理 */
    DSP_Spectrum_DeInit(&test_spectrum_handle);
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试频率测量功能
 */
static DSP_Status DSP_Spectrum_Test_FrequencyMeasurement(void)
{
    DSP_Status status;
    
    /* 初始化 */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    status = DSP_Spectrum_Init(&test_spectrum_handle, &test_fft_handle, &DSP_SPECTRUM_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 生成测试信号 */
    DSP_Spectrum_Test_GenerateTestSignal(test_signal, DSP_FFT_SIZE_512);
    
    /* 执行频谱分析 */
    status = DSP_Spectrum_Analyze(&test_spectrum_handle, test_signal, &test_result);
    if (status != DSP_STATUS_OK) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 验证频率测量结果 */
    if (test_result.frequency_centroid < 0.0f ||
        test_result.spectral_rolloff < 0.0f ||
        test_result.frequency_centroid > test_spectrum_handle.config.max_frequency) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 清理 */
    DSP_Spectrum_DeInit(&test_spectrum_handle);
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 测试功率计算功能
 */
static DSP_Status DSP_Spectrum_Test_PowerCalculation(void)
{
    DSP_Status status;
    
    /* 初始化 */
    status = DSP_FFT_Init(&test_fft_handle, &DSP_FFT_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    status = DSP_Spectrum_Init(&test_spectrum_handle, &test_fft_handle, &DSP_SPECTRUM_CONFIG_DEFAULT);
    if (status != DSP_STATUS_OK) {
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 生成测试信号 */
    DSP_Spectrum_Test_GenerateTestSignal(test_signal, DSP_FFT_SIZE_512);
    
    /* 执行频谱分析 */
    status = DSP_Spectrum_Analyze(&test_spectrum_handle, test_signal, &test_result);
    if (status != DSP_STATUS_OK) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return status;
    }
    
    /* 验证功率计算结果 */
    if (test_result.power_spectrum == NULL ||
        test_result.total_power <= 0.0f) {
        DSP_Spectrum_DeInit(&test_spectrum_handle);
        DSP_FFT_DeInit(&test_fft_handle);
        return DSP_STATUS_ERROR;
    }
    
    /* 清理 */
    DSP_Spectrum_DeInit(&test_spectrum_handle);
    DSP_FFT_DeInit(&test_fft_handle);
    
    return DSP_STATUS_OK;
}

/**
 * @brief 生成测试信号
 */
static void DSP_Spectrum_Test_GenerateTestSignal(DSP_Float *buffer, uint16_t length)
{
    DSP_Float sample_rate = DSP_SAMPLE_RATE_16K;
    
    /* 生成复合信号：1kHz + 3kHz + 噪声 */
    for (uint16_t i = 0; i < length; i++) {
        DSP_Float t = (DSP_Float)i / sample_rate;
        
        /* 1kHz正弦波 */
        DSP_Float signal1 = 0.8f * sinf(TWO_PI * 1000.0f * t);
        
        /* 3kHz正弦波 */
        DSP_Float signal2 = 0.5f * sinf(TWO_PI * 3000.0f * t);
        
        /* 少量噪声 */
        DSP_Float noise = 0.1f * ((DSP_Float)rand() / RAND_MAX - 0.5f);
        
        buffer[i] = signal1 + signal2 + noise;
    }
}
