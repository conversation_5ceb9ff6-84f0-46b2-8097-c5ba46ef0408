/**
 * @file send_info.h
 * @brief 统一处理发送的调试信息(对接void StateMachine_HandleDataSending(void))
 */

#ifndef SEND_INFO_H_
#define SEND_INFO_H_

#include "motor.h"
#include "time.h"
#include "usart.h"
#include "delay.h"
#include "oscilloscope.h"
#include <stdio.h>

// 系统常量定义
#define ENCODER_LINES           11      // 编码器线数
#define GEAR_RATIO              30      // 减速比 1:30
#define QEI_MULTIPLIER          4       // QEI 4倍频
#define PULSES_PER_REVOLUTION   (ENCODER_LINES * GEAR_RATIO * QEI_MULTIPLIER)  // 1320
#define MAX_MOTOR_RPM           3000    // 假设最大电机转速 (需要根据实际电机调整)
#define ENCODER_UPDATE_INTERVAL 100     // 编码器数据更新间隔 (ms)

extern char uart_buffer[1000];
extern int motor_speed; // 当前电机转速百分比
extern int adc_value; // ADC转换结果

/**
 * @brief 【电机】发送电机速度信息
 */
void Send_Motor_Speed(void);

/**
 * @brief 【电机】发送编码器数据
 */
void Send_Encoder_Data(void);

/**
 * @brief 【电机】发送调试信息
 */
void Send_Debug_Info(void);

/**
 * @brief 【电机】发送当前电机状态信息至PC
 */
void Send_Motordebug_info(void);

/**
 * @brief 【电机】发送当前电机状态信息至串口屏
 */
void Send_Debug_Info_Port(void);

/* 波形显示参数 */
#define OSC_REALY_HEIGHT        (double)355     // 波形显示区域实际高度(像素)
#define OSC_DISPLAY_HEIGHT      (double)255     // 波形显示区域映射高度
#define OSC_WAVEFORM_OBJECT     "s0.id" // 波形控件名称
#define OSC_WAVEFORM_CHANNEL    0       // 波形通道号
#define OSC_PATTERN_HEIGHT      60      // 上位机每个的像素高度

/**
 * @brief 【示波器】发送波形数据到上位机
 */
void Oscilloscope_SendWaveformData(void);

/**
 * @brief 【示波器】发送测量结果到上位机
 */
void Oscilloscope_SendMeasurement(void);

/**
 * @brief 【示波器】发送示波器状态到上位机
 */
void Oscilloscope_SendStatus(void);

#endif