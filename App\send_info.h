/**
 * @file send_info.h
 * @brief 统一处理发送的调试信息
 */

#ifndef SEND_INFO_H_
#define SEND_INFO_H_

#include "motor.h"
#include "time.h"
#include "usart.h"
#include "delay.h"
#include <stdio.h>

// 系统常量定义
#define ENCODER_LINES           11      // 编码器线数
#define GEAR_RATIO              30      // 减速比 1:30
#define QEI_MULTIPLIER          4       // QEI 4倍频
#define PULSES_PER_REVOLUTION   (ENCODER_LINES * GEAR_RATIO * QEI_MULTIPLIER)  // 1320
#define MAX_MOTOR_RPM           3000    // 假设最大电机转速 (需要根据实际电机调整)
#define ENCODER_UPDATE_INTERVAL 100     // 编码器数据更新间隔 (ms)

extern char uart_buffer[1000];
extern int motor_speed; // 当前电机转速百分比
extern int adc_value; // ADC转换结果

void Send_Motor_Speed(void); // 发送电机速度信息
void Send_Encoder_Data(void); // 发送编码器数据
void Send_Debug_Info(void); // 发送调试信息
void Send_Motordebug_info(void); //发送当前电机状态信息至PC
void Send_Debug_Info_Port(void); //发送当前电机状态信息至串口屏

#endif