******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Jul 25 16:22:33 2025

OUTPUT FILE NAME:   <key.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000045a5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004f78  0001b088  R  X
  SRAM                  20200000   00008000  00000d95  0000726b  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004f78   00004f78    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004890   00004890    r-x .text
  00004950    00004950    000005e0   000005e0    r-- .rodata
  00004f30    00004f30    00000048   00000048    r-- .cinit
20200000    20200000    00000b9c   00000000    rw-
  20200000    20200000    00000619   00000000    rw- .bss
  20200620    20200620    0000057c   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004890     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000003cc     oscilloscope.o (.text.Oscilloscope_MeasureWaveform)
                  00000e5c    000002f0     send_info.o (.text.Oscilloscope_SendWaveformData)
                  0000114c    000002e6     key.o (.text.KEY_ScanMultiple)
                  00001432    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001434    000002bc     usart.o (.text.UART_ProcessReceivedData)
                  000016f0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001910    000001e0     motor.o (.text.set_motor_speed)
                  00001af0    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001ccc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001e5e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001e60    0000017c     send_info.o (.text.Oscilloscope_SendStatus)
                  00001fdc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002118    00000134     oscilloscope.o (.text.Oscilloscope_ReadRealADC)
                  0000224c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  0000236c    0000011c     state_machine.o (.text.StateMachine_Process)
                  00002488    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002594    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002698    000000fc     send_info.o (.text.Send_Debug_Info_Port)
                  00002794    000000f4     state_machine.o (.text.StateMachine_HandleOscilloscopeControl)
                  00002888    000000f0     main.o (.text.KET_Event_NonBlocking)
                  00002978    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002a60    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002b44    000000e4     main.o (.text.main)
                  00002c28    000000e0     usart.o (.text.UART_SendBuffer)
                  00002d08    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002de0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002eb8    000000d4     usart.o (.text.UART_Init)
                  00002f8c    000000b0     usart.o (.text.UART_ProcessTJCData)
                  0000303c    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000030de    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000030e0    000000a0     libc.a : e_sqrtf.c.obj (.text.sqrtf)
                  00003180    0000009a            : memcpy16.S.obj (.text:memcpy)
                  0000321a    00000002     --HOLE-- [fill = 0]
                  0000321c    00000094     send_info.o (.text.Oscilloscope_SendMeasurement)
                  000032b0    00000092     tjc.o (.text.TJC_ParseByte)
                  00003342    00000002     --HOLE-- [fill = 0]
                  00003344    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_ADC_MOTOR_V_init)
                  000033d4    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_UART0_init)
                  00003464    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOER_A_init)
                  000034f0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000357c    00000088     oscilloscope.o (.text.Oscilloscope_Init)
                  00003604    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003686    00000002     --HOLE-- [fill = 0]
                  00003688    00000080     motor.o (.text.MOTOR_Init)
                  00003708    0000007c     state_machine.o (.text.StateMachine_HandleDataSending)
                  00003784    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003800    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003874    0000000c     adc.o (.text.ADC_GetLastResult)
                  00003880    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000038f4    00000070     oscilloscope.o (.text.Oscilloscope_DMAComplete)
                  00003964    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init)
                  000039d4    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003a44    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003aac    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003b12    00000002     --HOLE-- [fill = 0]
                  00003b14    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003b76    00000062     libc.a : memset16.S.obj (.text:memset)
                  00003bd8    0000005c     oscilloscope.o (.text.Oscilloscope_Start)
                  00003c34    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003c90    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00003ce8    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003d40    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003d98    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003dee    00000002     --HOLE-- [fill = 0]
                  00003df0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_QEI_0_init)
                  00003e44    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003e96    00000002     --HOLE-- [fill = 0]
                  00003e98    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003ee4    0000004c     time.o (.text.QEI_GetSpeed)
                  00003f30    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003f7a    00000002     --HOLE-- [fill = 0]
                  00003f7c    00000048     adc.o (.text.ADC_Init)
                  00003fc4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000400c    00000044     oscilloscope.o (.text.Oscilloscope_SingleTrigger)
                  00004050    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004094    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000040d4    00000040     key.o (.text.KEY_All_Init)
                  00004114    00000040     oscilloscope.o (.text.Oscilloscope_Stop)
                  00004154    00000040     state_machine.o (.text.StateMachine_FrameReceivedCallback)
                  00004194    00000040     usart.o (.text.UART_SendString)
                  000041d4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004214    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004254    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004294    0000003c            : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000042d0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000430a    00000002     --HOLE-- [fill = 0]
                  0000430c    00000038     adc.o (.text.ADC_DMA_TransferComplete)
                  00004344    00000038     adc.o (.text.ADC_StartConversion)
                  0000437c    00000038     main.o (.text.DMA_IRQHandler)
                  000043b4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000043ec    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004424    00000036     libclang_rt.builtins.a : floatundisf.S.obj (.text.__floatundisf)
                  0000445a    00000002     --HOLE-- [fill = 0]
                  0000445c    00000034     time.o (.text.timer_init)
                  00004490    00000032     libclang_rt.builtins.a : fixunssfsi.S.obj (.text.__fixunssfsi)
                  000044c2    00000002     --HOLE-- [fill = 0]
                  000044c4    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000044f4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004524    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_A1_init)
                  00004550    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000457c    00000028                            : floatunsisf.S.obj (.text.__floatunsisf)
                  000045a4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000045cc    00000024     oscilloscope.o (.text.Oscilloscope_ForceTrigger)
                  000045f0    00000024     state_machine.o (.text.StateMachine_Init)
                  00004614    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004638    00000024                            : muldi3.S.obj (.text.__muldi3)
                  0000465c    00000024     delay.o (.text.delay_ms)
                  00004680    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000046a2    00000002     --HOLE-- [fill = 0]
                  000046a4    00000020     oscilloscope.o (.text.Oscilloscope_SetTrigger)
                  000046c4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000046e2    00000002     --HOLE-- [fill = 0]
                  000046e4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004700    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000471c    0000001c     oscilloscope.o (.text.Oscilloscope_ClearWaveform)
                  00004738    0000001c     delay.o (.text.delay_init)
                  00004754    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000476c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00004784    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  0000479c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH2_init)
                  000047b4    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000047cc    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000047e0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000047f4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004806    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00004818    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004828    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00004838    00000010     delay.o (.text.SysTick_Handler)
                  00004848    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004858    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00004868    0000000e     key.o (.text.KEY_GetEvent)
                  00004876    00000002     --HOLE-- [fill = 0]
                  00004878    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004886    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004894    0000000c     adc.o (.text.ADC_IsConversionComplete)
                  000048a0    0000000c     time.o (.text.QEI_GetPosition)
                  000048ac    0000000c     usart.o (.text.UART0_IRQHandler)
                  000048b8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000048c4    0000000c     delay.o (.text.get_tick_ms)
                  000048d0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000048da    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000048e4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000048f4    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000048fe    0000000a     tjc.o (.text.TJC_ParserInit)
                  00004908    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00004912    00000008     main.o (.text.TJC_FrameReceivedCallback)
                  0000491a    00000002     --HOLE-- [fill = 0]
                  0000491c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004924    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000492c    00000006     libc.a : exit.c.obj (.text:abort)
                  00004932    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004936    00000004     time.o (.text.QEI_GetDirection)
                  0000493a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000493e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00004942    0000000e     --HOLE-- [fill = 0]

.cinit     0    00004f30    00000048     
                  00004f30    0000001d     (.cinit..data.load) [load image, compression = lzss]
                  00004f4d    00000003     --HOLE-- [fill = 0]
                  00004f50    0000000c     (__TI_handler_table)
                  00004f5c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004f64    00000010     (__TI_cinit_table)
                  00004f74    00000004     --HOLE-- [fill = 0]

.rodata    0    00004950    000005e0     
                  00004950    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004a51    00000034     motor.o (.rodata.str1.12748093362806134514.1)
                  00004a85    00000034     motor.o (.rodata.str1.7408246413643671320.1)
                  00004ab9    00000031     motor.o (.rodata.str1.5850567729483738290.1)
                  00004aea    0000002e     oscilloscope.o (.rodata.str1.3789914365216800215.1)
                  00004b18    0000002c     oscilloscope.o (.rodata.str1.17501530167750222819.1)
                  00004b44    0000002b     state_machine.o (.rodata.str1.5499806488662909927.1)
                  00004b6f    00000027     usart.o (.rodata.str1.10308446891049622352.1)
                  00004b96    00000002     ti_msp_dl_config.o (.rodata.gUSER_UART0ClockConfig)
                  00004b98    00000026     main.o (.rodata.str1.8154729771448623357.4)
                  00004bbe    00000024     motor.o (.rodata.str1.10718775090649846465.1)
                  00004be2    00000020     state_machine.o (.rodata.str1.17285445957577556728.1)
                  00004c02    00000002     --HOLE-- [fill = 0]
                  00004c04    0000001f     main.o (.rodata.str1.18227636981041470289.4)
                  00004c23    00000001     --HOLE-- [fill = 0]
                  00004c24    0000001c     main.o (.rodata.str1.17100691992556644108.4)
                  00004c40    0000001b     state_machine.o (.rodata.str1.10278809154692732284.4)
                  00004c5b    0000001b     main.o (.rodata.str1.15159059442110792349.1)
                  00004c76    00000019     send_info.o (.rodata.str1.5279943184275334214.1)
                  00004c8f    00000001     --HOLE-- [fill = 0]
                  00004c90    00000018     usart.o (.rodata..L__const.UART_Init.dmaConfig)
                  00004ca8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00004cc0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00004cd8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH2Config)
                  00004cf0    00000018     state_machine.o (.rodata.str1.3855662077583193624.4)
                  00004d08    00000017     send_info.o (.rodata.str1.15238459376144547403.1)
                  00004d1f    00000016     send_info.o (.rodata.str1.12343814962941690147.1)
                  00004d35    00000003     send_info.o (.rodata.Port_End.end_bytes)
                  00004d38    00000015     state_machine.o (.rodata.str1.12802625103307508707.4)
                  00004d4d    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTOER_AClockConfig)
                  00004d50    00000015     state_machine.o (.rodata.str1.13712074210919037681.4)
                  00004d65    00000003     ti_msp_dl_config.o (.rodata.gTIMER_A1ClockConfig)
                  00004d68    00000015     state_machine.o (.rodata.str1.14045022949785916042.4)
                  00004d7d    00000003     ti_msp_dl_config.o (.rodata.gUSER_QEI_0ClockConfig)
                  00004d80    00000015     state_machine.o (.rodata.str1.14502826223124610429.4)
                  00004d95    00000003     send_info.o (.rodata.str1.14198352196226650032.1)
                  00004d98    00000014     ti_msp_dl_config.o (.rodata.gTIMER_A1TimerConfig)
                  00004dac    00000014     send_info.o (.rodata.str1.14536835306665630867.1)
                  00004dc0    00000014     send_info.o (.rodata.str1.17094292589337375441.1)
                  00004dd4    00000014     send_info.o (.rodata.str1.6049280976157928166.1)
                  00004de8    00000013     send_info.o (.rodata.str1.12854150023414117050.4)
                  00004dfb    00000001     --HOLE-- [fill = 0]
                  00004dfc    00000013     send_info.o (.rodata.str1.5883603548910157963.4)
                  00004e0f    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004e20    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004e31    00000010     send_info.o (.rodata.str1.2617261163612467819.1)
                  00004e41    00000010     send_info.o (.rodata.str1.8500294019303210070.1)
                  00004e51    0000000f     send_info.o (.rodata.str1.16606331108790205631.1)
                  00004e60    0000000f     state_machine.o (.rodata.str1.16700323032500373959.4)
                  00004e6f    0000000f     send_info.o (.rodata.str1.17229586732647165751.1)
                  00004e7e    0000000f     send_info.o (.rodata.str1.2450867416504314851.1)
                  00004e8d    0000000f     send_info.o (.rodata.str1.3760426283636117217.1)
                  00004e9c    0000000e     send_info.o (.rodata.str1.13342906380623343076.1)
                  00004eaa    0000000d     send_info.o (.rodata.str1.15475178832324618403.1)
                  00004eb7    0000000d     send_info.o (.rodata.str1.6503431098811812287.1)
                  00004ec4    0000000d     send_info.o (.rodata.str1.766284786160762975.1)
                  00004ed1    00000003     --HOLE-- [fill = 0]
                  00004ed4    0000000c     send_info.o (.rodata..Lswitch.table.Oscilloscope_SendStatus)
                  00004ee0    0000000a     ti_msp_dl_config.o (.rodata.gUSER_UART0Config)
                  00004eea    00000002     --HOLE-- [fill = 0]
                  00004eec    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTOER_AConfig)
                  00004ef4    00000008     ti_msp_dl_config.o (.rodata.gUSER_ADC_MOTOR_VClockConfig)
                  00004efc    00000008     ti_msp_dl_config.o (.rodata.gUSER_ADC_OSCILLOSCOPEClockConfig)
                  00004f04    00000008     send_info.o (.rodata.str1.15772858833589009475.1)
                  00004f0c    00000007     send_info.o (.rodata.str1.12466795250469521396.1)
                  00004f13    00000007     send_info.o (.rodata.str1.15018526337455767769.1)
                  00004f1a    00000007     send_info.o (.rodata.str1.8765837587910983436.1)
                  00004f21    00000006     send_info.o (.rodata.str1.9939855383378986149.1)
                  00004f27    00000005     send_info.o (.rodata.str1.3264826402484333313.1)
                  00004f2c    00000004     send_info.o (.rodata.str1.1285647927986884111.1)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000619     UNINITIALIZED
                  20200000    000003e8     (.common:uart_buffer)
                  202003e8    000000bc     (.common:gTIMER_A1Backup)
                  202004a4    000000a0     (.common:gUSER_QEI_0Backup)
                  20200544    00000040     usart.o (.bss.rxProcessBuffer)
                  20200584    00000028     (.common:userKey)
                  202005ac    00000028     (.common:userKey1)
                  202005d4    00000025     (.common:tjc_parser)
                  202005f9    00000020     (.common:gRxPacket)

.data      0    20200620    0000057c     UNINITIALIZED
                  20200620    000004d8     oscilloscope.o (.data.osc_config)
                  20200af8    00000028     state_machine.o (.data.sm_state)
                  20200b20    00000008     main.o (.data.allKeys)
                  20200b28    00000004     adc.o (.data.ADC_DMA_TransferComplete.dma_complete_count)
                  20200b2c    00000004     adc.o (.data.ADC_StartConversion.start_count)
                  20200b30    00000004     state_machine.o (.data.StateMachine_HandleDataSending.last_serial_time)
                  20200b34    00000004     state_machine.o (.data.StateMachine_HandleDataSending.last_status_time)
                  20200b38    00000004     state_machine.o (.data.StateMachine_HandleMotorSpeedIncrease.motor_speed)
                  20200b3c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200b40    00000004     adc.o (.data.adc_result)
                  20200b44    00000004     main.o (.data.adc_value)
                  20200b48    00000004     usart.o (.data.bytesProcessedCount)
                  20200b4c    00000004     main.o (.data.dma_ch1_interrupt_count)
                  20200b50    00000004     time.o (.data.encoderSpeed)
                  20200b54    00000004     send_info.o (.data.encoder_position)
                  20200b58    00000004     send_info.o (.data.encoder_speed)
                  20200b5c    00000004     delay.o (.data.g_tick_ms)
                  20200b60    00000004     usart.o (.data.lastDmaTransferSize)
                  20200b64    00000004     time.o (.data.lastPosition)
                  20200b68    00000004     time.o (.data.lastTime)
                  20200b6c    00000004     main.o (.data.last_adc_sample_time)
                  20200b70    00000004     main.o (.data.last_key_scan_time)
                  20200b74    00000004     send_info.o (.data.last_position)
                  20200b78    00000004     main.o (.data.led_blink_time)
                  20200b7c    00000004     motor.o (.data.motor_speed)
                  20200b80    00000004     oscilloscope.o (.data.osc_dma_interrupt_count)
                  20200b84    00000004     motor.o (.data.period)
                  20200b88    00000004     usart.o (.data.stableCount)
                  20200b8c    00000002     adc.o (.data.adc_dma_buffer)
                  20200b8e    00000002     main.o (.data.prevKeyEvent)
                  20200b90    00000002     usart.o (.data.rxAvailableBytes)
                  20200b92    00000002     usart.o (.data.rxProcessIndex)
                  20200b94    00000001     main.o (.data.Handle_ADC_Sampling_NonBlocking.adc_state)
                  20200b95    00000001     adc.o (.data.adc_conversion_complete)
                  20200b96    00000001     send_info.o (.data.encoder_direction)
                  20200b97    00000001     usart.o (.data.gRxComplete)
                  20200b98    00000001     main.o (.data.key_encoder_reset)
                  20200b99    00000001     main.o (.data.led_blink_active)
                  20200b9a    00000001     motor.o (.data.motor_init)
                  20200b9b    00000001     state_machine.o (.data.oscilloscope_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       main.o                         532     124       1113   
       ti_msp_dl_config.o             1072    137       348    
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1610    453       1461   
                                                               
    .\App\
       oscilloscope.o                 1848    90        1244   
       send_info.o                    1532    375       13     
       state_machine.o                752     225       53     
       tjc.o                          156     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         4288    690       1347   
                                                               
    .\BSP\
       usart.o                        1388    63        113    
       key.o                          820     0         0      
       motor.o                        608     189       9      
       adc.o                          208     0         15     
       time.o                         144     0         12     
       delay.o                        92      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         3260    252       153    
                                                               
    F:/Ti/ccs/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         828     0         0      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       e_sqrtf.c.obj                  160     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5798    291       4      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatundisf.S.obj              54      0         0      
       fixunssfsi.S.obj               50      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2746    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       65        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   18534   1751      3477   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004f64 records: 2, size/record: 8, table size: 16
	.data: load addr=00004f30, load size=0000001d bytes, run addr=20200620, run size=0000057c bytes, compression=lzss
	.bss: load addr=00004f5c, load size=00000008 bytes, run addr=20200000, run size=00000619 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004f50 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001ccd     000048e4     000048e2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                  
-------   ----                                  
00001433  ADC0_IRQHandler                       
00001433  ADC1_IRQHandler                       
0000430d  ADC_DMA_TransferComplete              
00003875  ADC_GetLastResult                     
00003f7d  ADC_Init                              
00004895  ADC_IsConversionComplete              
00004345  ADC_StartConversion                   
00001433  AES_IRQHandler                        
00004932  C$$EXIT                               
00001433  CANFD0_IRQHandler                     
00001433  DAC0_IRQHandler                       
00004095  DL_ADC12_setClockConfig               
000048d1  DL_Common_delayCycles                 
00003e99  DL_DMA_initChannel                    
00002595  DL_Timer_initFourCCPWMMode            
00002979  DL_Timer_initTimerMode                
000046e5  DL_Timer_setCaptCompUpdateMethod      
00004755  DL_Timer_setCaptureCompareOutCtl      
00004819  DL_Timer_setCaptureCompareValue       
00004701  DL_Timer_setClockConfig               
00003fc5  DL_UART_init                          
000047f5  DL_UART_setClockConfig                
0000437d  DMA_IRQHandler                        
00001433  Default_Handler                       
00001433  GROUP0_IRQHandler                     
00001433  GROUP1_IRQHandler                     
00004933  HOSTexit                              
00001433  HardFault_Handler                     
00001433  I2C0_IRQHandler                       
00001433  I2C1_IRQHandler                       
00002889  KET_Event_NonBlocking                 
000040d5  KEY_All_Init                          
00004869  KEY_GetEvent                          
0000114d  KEY_ScanMultiple                      
00003689  MOTOR_Init                            
00001433  NMI_Handler                           
0000471d  Oscilloscope_ClearWaveform            
000038f5  Oscilloscope_DMAComplete              
000045cd  Oscilloscope_ForceTrigger             
0000357d  Oscilloscope_Init                     
00000a91  Oscilloscope_MeasureWaveform          
00002119  Oscilloscope_ReadRealADC              
0000321d  Oscilloscope_SendMeasurement          
00001e61  Oscilloscope_SendStatus               
00000e5d  Oscilloscope_SendWaveformData         
000046a5  Oscilloscope_SetTrigger               
0000400d  Oscilloscope_SingleTrigger            
00003bd9  Oscilloscope_Start                    
00004115  Oscilloscope_Stop                     
00001433  PendSV_Handler                        
00004937  QEI_GetDirection                      
000048a1  QEI_GetPosition                       
00003ee5  QEI_GetSpeed                          
00001433  RTC_IRQHandler                        
0000493b  Reset_Handler                         
00001433  SPI0_IRQHandler                       
00001433  SPI1_IRQHandler                       
00001433  SVC_Handler                           
0000476d  SYSCFG_DL_DMA_CH0_init                
00004785  SYSCFG_DL_DMA_CH1_init                
0000479d  SYSCFG_DL_DMA_CH2_init                
00004829  SYSCFG_DL_DMA_init                    
00003c91  SYSCFG_DL_GPIO_init                   
00003465  SYSCFG_DL_PWM_MOTOER_A_init           
000044c5  SYSCFG_DL_SYSCTL_init                 
00004525  SYSCFG_DL_TIMER_A1_init               
00003345  SYSCFG_DL_USER_ADC_MOTOR_V_init       
00003965  SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init  
00003df1  SYSCFG_DL_USER_QEI_0_init             
000033d5  SYSCFG_DL_USER_UART0_init             
00004051  SYSCFG_DL_init                        
000039d5  SYSCFG_DL_initPower                   
00002699  Send_Debug_Info_Port                  
00004155  StateMachine_FrameReceivedCallback    
00003709  StateMachine_HandleDataSending        
00002795  StateMachine_HandleOscilloscopeControl
000045f1  StateMachine_Init                     
0000236d  StateMachine_Process                  
00004839  SysTick_Handler                       
00001433  TIMA0_IRQHandler                      
00001433  TIMA1_IRQHandler                      
00001433  TIMG0_IRQHandler                      
00001433  TIMG12_IRQHandler                     
00001433  TIMG6_IRQHandler                      
00001433  TIMG7_IRQHandler                      
00001433  TIMG8_IRQHandler                      
00004913  TJC_FrameReceivedCallback             
000032b1  TJC_ParseByte                         
000048ff  TJC_ParserInit                        
000048ad  UART0_IRQHandler                      
00001433  UART1_IRQHandler                      
00001433  UART2_IRQHandler                      
00001433  UART3_IRQHandler                      
00002eb9  UART_Init                             
00001435  UART_ProcessReceivedData              
00002f8d  UART_ProcessTJCData                   
00002c29  UART_SendBuffer                       
00004195  UART_SendString                       
20208000  __STACK_END                           
00000200  __STACK_SIZE                          
00000000  __TI_ATRegion0_region_sz              
00000000  __TI_ATRegion0_src_addr               
00000000  __TI_ATRegion0_trg_addr               
00000000  __TI_ATRegion1_region_sz              
00000000  __TI_ATRegion1_src_addr               
00000000  __TI_ATRegion1_trg_addr               
00000000  __TI_ATRegion2_region_sz              
00000000  __TI_ATRegion2_src_addr               
00000000  __TI_ATRegion2_trg_addr               
00004f64  __TI_CINIT_Base                       
00004f74  __TI_CINIT_Limit                      
00004f74  __TI_CINIT_Warm                       
00004f50  __TI_Handler_Table_Base               
00004f5c  __TI_Handler_Table_Limit              
00004295  __TI_auto_init_nobinit_nopinit        
00003785  __TI_decompress_lzss                  
00004807  __TI_decompress_none                  
00003ce9  __TI_ltoa                             
ffffffff  __TI_pprof_out_hndl                   
000000c1  __TI_printfi                          
ffffffff  __TI_prof_data_size                   
ffffffff  __TI_prof_data_start                  
00000000  __TI_static_base__                    
00004859  __TI_zero_init                        
00001cd7  __adddf3                              
00002deb  __addsf3                              
00004950  __aeabi_ctype_table_                  
00004950  __aeabi_ctype_table_C                 
00003881  __aeabi_d2f                           
00003f31  __aeabi_d2iz                          
00001cd7  __aeabi_dadd                          
00003b15  __aeabi_dcmpeq                        
00003b51  __aeabi_dcmpge                        
00003b65  __aeabi_dcmpgt                        
00003b3d  __aeabi_dcmple                        
00003b29  __aeabi_dcmplt                        
00002489  __aeabi_ddiv                          
00002a61  __aeabi_dmul                          
00001ccd  __aeabi_dsub                          
20200b3c  __aeabi_errno                         
0000491d  __aeabi_errno_addr                    
00004215  __aeabi_f2d                           
000043b5  __aeabi_f2iz                          
00004491  __aeabi_f2uiz                         
00002deb  __aeabi_fadd                          
00003605  __aeabi_fdiv                          
000034f1  __aeabi_fmul                          
00002de1  __aeabi_fsub                          
00004551  __aeabi_i2d                           
00003d99  __aeabi_idiv                          
00001e5f  __aeabi_idiv0                         
00003d99  __aeabi_idivmod                       
000030df  __aeabi_ldiv0                         
000046c5  __aeabi_llsl                          
00004639  __aeabi_lmul                          
000048b9  __aeabi_memclr                        
000048b9  __aeabi_memclr4                       
000048b9  __aeabi_memclr8                       
00004925  __aeabi_memcpy                        
00004925  __aeabi_memcpy4                       
00004925  __aeabi_memcpy8                       
00004879  __aeabi_memset                        
00004879  __aeabi_memset4                       
00004879  __aeabi_memset8                       
00004615  __aeabi_ui2d                          
0000457d  __aeabi_ui2f                          
000041d5  __aeabi_uidiv                         
000041d5  __aeabi_uidivmod                      
00004425  __aeabi_ul2f                          
000047cd  __aeabi_uldivmod                      
000046c5  __ashldi3                             
ffffffff  __binit__                             
00003a45  __cmpdf2                              
00002489  __divdf3                              
00003605  __divsf3                              
00003a45  __eqdf2                               
00004215  __extendsfdf2                         
00003f31  __fixdfsi                             
000043b5  __fixsfsi                             
00004491  __fixunssfsi                          
00004551  __floatsidf                           
00004425  __floatundisf                         
00004615  __floatunsidf                         
0000457d  __floatunsisf                         
00003801  __gedf2                               
00003801  __gtdf2                               
00003a45  __ledf2                               
00003a45  __ltdf2                               
UNDEFED   __mpu_init                            
00002a61  __muldf3                              
00004639  __muldi3                              
000042d1  __muldsi3                             
000034f1  __mulsf3                              
00003a45  __nedf2                               
20207e00  __stack                               
20200000  __start___llvm_prf_bits               
20200000  __start___llvm_prf_cnts               
20200000  __stop___llvm_prf_bits                
20200000  __stop___llvm_prf_cnts                
00001ccd  __subdf3                              
00002de1  __subsf3                              
00003881  __truncdfsf2                          
0000303d  __udivmoddi4                          
000045a5  _c_int00_noargs                       
UNDEFED   _system_post_cinit                    
0000493f  _system_pre_init                      
0000492d  abort                                 
20200b44  adc_value                             
20200b20  allKeys                               
00004255  atoi                                  
ffffffff  binit                                 
00004739  delay_init                            
0000465d  delay_ms                              
20200b4c  dma_ch1_interrupt_count               
20200b96  encoder_direction                     
20200b54  encoder_position                      
20200b58  encoder_speed                         
00003c35  frexp                                 
00003c35  frexpl                                
20200b97  gRxComplete                           
202005f9  gRxPacket                             
202003e8  gTIMER_A1Backup                       
202004a4  gUSER_QEI_0Backup                     
000048c5  get_tick_ms                           
00000000  interruptVectors                      
20200b98  key_encoder_reset                     
20200b74  last_position                         
00002d09  ldexp                                 
00002d09  ldexpl                                
00002b45  main                                  
00004681  memccpy                               
00003181  memcpy                                
00003b77  memset                                
20200b9a  motor_init                            
20200b7c  motor_speed                           
20200620  osc_config                            
20200b80  osc_dma_interrupt_count               
20200b84  period                                
20200b8e  prevKeyEvent                          
00002d09  scalbn                                
00002d09  scalbnl                               
00001911  set_motor_speed                       
20200af8  sm_state                              
000043ed  sprintf                               
000030e1  sqrtf                                 
0000445d  timer_init                            
202005d4  tjc_parser                            
20200000  uart_buffer                           
20200584  userKey                               
202005ac  userKey1                              
00004849  wcslen                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                  
-------   ----                                  
00000000  __TI_ATRegion0_region_sz              
00000000  __TI_ATRegion0_src_addr               
00000000  __TI_ATRegion0_trg_addr               
00000000  __TI_ATRegion1_region_sz              
00000000  __TI_ATRegion1_src_addr               
00000000  __TI_ATRegion1_trg_addr               
00000000  __TI_ATRegion2_region_sz              
00000000  __TI_ATRegion2_src_addr               
00000000  __TI_ATRegion2_trg_addr               
00000000  __TI_static_base__                    
00000000  interruptVectors                      
000000c1  __TI_printfi                          
00000200  __STACK_SIZE                          
00000a91  Oscilloscope_MeasureWaveform          
00000e5d  Oscilloscope_SendWaveformData         
0000114d  KEY_ScanMultiple                      
00001433  ADC0_IRQHandler                       
00001433  ADC1_IRQHandler                       
00001433  AES_IRQHandler                        
00001433  CANFD0_IRQHandler                     
00001433  DAC0_IRQHandler                       
00001433  Default_Handler                       
00001433  GROUP0_IRQHandler                     
00001433  GROUP1_IRQHandler                     
00001433  HardFault_Handler                     
00001433  I2C0_IRQHandler                       
00001433  I2C1_IRQHandler                       
00001433  NMI_Handler                           
00001433  PendSV_Handler                        
00001433  RTC_IRQHandler                        
00001433  SPI0_IRQHandler                       
00001433  SPI1_IRQHandler                       
00001433  SVC_Handler                           
00001433  TIMA0_IRQHandler                      
00001433  TIMA1_IRQHandler                      
00001433  TIMG0_IRQHandler                      
00001433  TIMG12_IRQHandler                     
00001433  TIMG6_IRQHandler                      
00001433  TIMG7_IRQHandler                      
00001433  TIMG8_IRQHandler                      
00001433  UART1_IRQHandler                      
00001433  UART2_IRQHandler                      
00001433  UART3_IRQHandler                      
00001435  UART_ProcessReceivedData              
00001911  set_motor_speed                       
00001ccd  __aeabi_dsub                          
00001ccd  __subdf3                              
00001cd7  __adddf3                              
00001cd7  __aeabi_dadd                          
00001e5f  __aeabi_idiv0                         
00001e61  Oscilloscope_SendStatus               
00002119  Oscilloscope_ReadRealADC              
0000236d  StateMachine_Process                  
00002489  __aeabi_ddiv                          
00002489  __divdf3                              
00002595  DL_Timer_initFourCCPWMMode            
00002699  Send_Debug_Info_Port                  
00002795  StateMachine_HandleOscilloscopeControl
00002889  KET_Event_NonBlocking                 
00002979  DL_Timer_initTimerMode                
00002a61  __aeabi_dmul                          
00002a61  __muldf3                              
00002b45  main                                  
00002c29  UART_SendBuffer                       
00002d09  ldexp                                 
00002d09  ldexpl                                
00002d09  scalbn                                
00002d09  scalbnl                               
00002de1  __aeabi_fsub                          
00002de1  __subsf3                              
00002deb  __addsf3                              
00002deb  __aeabi_fadd                          
00002eb9  UART_Init                             
00002f8d  UART_ProcessTJCData                   
0000303d  __udivmoddi4                          
000030df  __aeabi_ldiv0                         
000030e1  sqrtf                                 
00003181  memcpy                                
0000321d  Oscilloscope_SendMeasurement          
000032b1  TJC_ParseByte                         
00003345  SYSCFG_DL_USER_ADC_MOTOR_V_init       
000033d5  SYSCFG_DL_USER_UART0_init             
00003465  SYSCFG_DL_PWM_MOTOER_A_init           
000034f1  __aeabi_fmul                          
000034f1  __mulsf3                              
0000357d  Oscilloscope_Init                     
00003605  __aeabi_fdiv                          
00003605  __divsf3                              
00003689  MOTOR_Init                            
00003709  StateMachine_HandleDataSending        
00003785  __TI_decompress_lzss                  
00003801  __gedf2                               
00003801  __gtdf2                               
00003875  ADC_GetLastResult                     
00003881  __aeabi_d2f                           
00003881  __truncdfsf2                          
000038f5  Oscilloscope_DMAComplete              
00003965  SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init  
000039d5  SYSCFG_DL_initPower                   
00003a45  __cmpdf2                              
00003a45  __eqdf2                               
00003a45  __ledf2                               
00003a45  __ltdf2                               
00003a45  __nedf2                               
00003b15  __aeabi_dcmpeq                        
00003b29  __aeabi_dcmplt                        
00003b3d  __aeabi_dcmple                        
00003b51  __aeabi_dcmpge                        
00003b65  __aeabi_dcmpgt                        
00003b77  memset                                
00003bd9  Oscilloscope_Start                    
00003c35  frexp                                 
00003c35  frexpl                                
00003c91  SYSCFG_DL_GPIO_init                   
00003ce9  __TI_ltoa                             
00003d99  __aeabi_idiv                          
00003d99  __aeabi_idivmod                       
00003df1  SYSCFG_DL_USER_QEI_0_init             
00003e99  DL_DMA_initChannel                    
00003ee5  QEI_GetSpeed                          
00003f31  __aeabi_d2iz                          
00003f31  __fixdfsi                             
00003f7d  ADC_Init                              
00003fc5  DL_UART_init                          
0000400d  Oscilloscope_SingleTrigger            
00004051  SYSCFG_DL_init                        
00004095  DL_ADC12_setClockConfig               
000040d5  KEY_All_Init                          
00004115  Oscilloscope_Stop                     
00004155  StateMachine_FrameReceivedCallback    
00004195  UART_SendString                       
000041d5  __aeabi_uidiv                         
000041d5  __aeabi_uidivmod                      
00004215  __aeabi_f2d                           
00004215  __extendsfdf2                         
00004255  atoi                                  
00004295  __TI_auto_init_nobinit_nopinit        
000042d1  __muldsi3                             
0000430d  ADC_DMA_TransferComplete              
00004345  ADC_StartConversion                   
0000437d  DMA_IRQHandler                        
000043b5  __aeabi_f2iz                          
000043b5  __fixsfsi                             
000043ed  sprintf                               
00004425  __aeabi_ul2f                          
00004425  __floatundisf                         
0000445d  timer_init                            
00004491  __aeabi_f2uiz                         
00004491  __fixunssfsi                          
000044c5  SYSCFG_DL_SYSCTL_init                 
00004525  SYSCFG_DL_TIMER_A1_init               
00004551  __aeabi_i2d                           
00004551  __floatsidf                           
0000457d  __aeabi_ui2f                          
0000457d  __floatunsisf                         
000045a5  _c_int00_noargs                       
000045cd  Oscilloscope_ForceTrigger             
000045f1  StateMachine_Init                     
00004615  __aeabi_ui2d                          
00004615  __floatunsidf                         
00004639  __aeabi_lmul                          
00004639  __muldi3                              
0000465d  delay_ms                              
00004681  memccpy                               
000046a5  Oscilloscope_SetTrigger               
000046c5  __aeabi_llsl                          
000046c5  __ashldi3                             
000046e5  DL_Timer_setCaptCompUpdateMethod      
00004701  DL_Timer_setClockConfig               
0000471d  Oscilloscope_ClearWaveform            
00004739  delay_init                            
00004755  DL_Timer_setCaptureCompareOutCtl      
0000476d  SYSCFG_DL_DMA_CH0_init                
00004785  SYSCFG_DL_DMA_CH1_init                
0000479d  SYSCFG_DL_DMA_CH2_init                
000047cd  __aeabi_uldivmod                      
000047f5  DL_UART_setClockConfig                
00004807  __TI_decompress_none                  
00004819  DL_Timer_setCaptureCompareValue       
00004829  SYSCFG_DL_DMA_init                    
00004839  SysTick_Handler                       
00004849  wcslen                                
00004859  __TI_zero_init                        
00004869  KEY_GetEvent                          
00004879  __aeabi_memset                        
00004879  __aeabi_memset4                       
00004879  __aeabi_memset8                       
00004895  ADC_IsConversionComplete              
000048a1  QEI_GetPosition                       
000048ad  UART0_IRQHandler                      
000048b9  __aeabi_memclr                        
000048b9  __aeabi_memclr4                       
000048b9  __aeabi_memclr8                       
000048c5  get_tick_ms                           
000048d1  DL_Common_delayCycles                 
000048ff  TJC_ParserInit                        
00004913  TJC_FrameReceivedCallback             
0000491d  __aeabi_errno_addr                    
00004925  __aeabi_memcpy                        
00004925  __aeabi_memcpy4                       
00004925  __aeabi_memcpy8                       
0000492d  abort                                 
00004932  C$$EXIT                               
00004933  HOSTexit                              
00004937  QEI_GetDirection                      
0000493b  Reset_Handler                         
0000493f  _system_pre_init                      
00004950  __aeabi_ctype_table_                  
00004950  __aeabi_ctype_table_C                 
00004f50  __TI_Handler_Table_Base               
00004f5c  __TI_Handler_Table_Limit              
00004f64  __TI_CINIT_Base                       
00004f74  __TI_CINIT_Limit                      
00004f74  __TI_CINIT_Warm                       
20200000  __start___llvm_prf_bits               
20200000  __start___llvm_prf_cnts               
20200000  __stop___llvm_prf_bits                
20200000  __stop___llvm_prf_cnts                
20200000  uart_buffer                           
202003e8  gTIMER_A1Backup                       
202004a4  gUSER_QEI_0Backup                     
20200584  userKey                               
202005ac  userKey1                              
202005d4  tjc_parser                            
202005f9  gRxPacket                             
20200620  osc_config                            
20200af8  sm_state                              
20200b20  allKeys                               
20200b3c  __aeabi_errno                         
20200b44  adc_value                             
20200b4c  dma_ch1_interrupt_count               
20200b54  encoder_position                      
20200b58  encoder_speed                         
20200b74  last_position                         
20200b7c  motor_speed                           
20200b80  osc_dma_interrupt_count               
20200b84  period                                
20200b8e  prevKeyEvent                          
20200b96  encoder_direction                     
20200b97  gRxComplete                           
20200b98  key_encoder_reset                     
20200b9a  motor_init                            
20207e00  __stack                               
20208000  __STACK_END                           
ffffffff  __TI_pprof_out_hndl                   
ffffffff  __TI_prof_data_size                   
ffffffff  __TI_prof_data_start                  
ffffffff  __binit__                             
ffffffff  binit                                 
UNDEFED   __mpu_init                            
UNDEFED   _system_post_cinit                    

[251 symbols]
