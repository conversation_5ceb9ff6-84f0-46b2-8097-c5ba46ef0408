******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Jul 25 15:43:56 2025

OUTPUT FILE NAME:   <key.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000045f5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004ef8  0001b108  R  X
  SRAM                  20200000   00008000  00000d95  0000726b  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004ef8   00004ef8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000048b0   000048b0    r-x .text
  00004970    00004970    00000540   00000540    r-- .rodata
  00004eb0    00004eb0    00000048   00000048    r-- .cinit
20200000    20200000    00000b9c   00000000    rw-
  20200000    20200000    00000619   00000000    rw- .bss
  20200620    20200620    0000057c   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000048b0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000003cc     oscilloscope.o (.text.Oscilloscope_MeasureWaveform)
                  00000e5c    000002f0     oscilloscope.o (.text.Oscilloscope_SendWaveformData)
                  0000114c    000002e6     key.o (.text.KEY_ScanMultiple)
                  00001432    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001434    000002bc     usart.o (.text.UART_ProcessReceivedData)
                  000016f0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001910    000001e0     motor.o (.text.set_motor_speed)
                  00001af0    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001ccc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001e5e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001e60    0000017c     oscilloscope.o (.text.Oscilloscope_SendStatus)
                  00001fdc    00000144     state_machine.o (.text.StateMachine_HandleOscilloscopeControl)
                  00002120    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000225c    00000134     oscilloscope.o (.text.Oscilloscope_ReadRealADC)
                  00002390    00000124     state_machine.o (.text.StateMachine_Process)
                  000024b4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000025d4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000026e0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000027e4    000000fc     send_info.o (.text.Send_Debug_Info_Port)
                  000028e0    000000f0     main.o (.text.KET_Event_NonBlocking)
                  000029d0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002ab8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002b9c    000000e4     main.o (.text.main)
                  00002c80    000000e0     usart.o (.text.UART_SendBuffer)
                  00002d60    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002e38    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002f10    000000d4     usart.o (.text.UART_Init)
                  00002fe4    000000b0     usart.o (.text.UART_ProcessTJCData)
                  00003094    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003136    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003138    000000a0     libc.a : e_sqrtf.c.obj (.text.sqrtf)
                  000031d8    0000009a            : memcpy16.S.obj (.text:memcpy)
                  00003272    00000002     --HOLE-- [fill = 0]
                  00003274    00000094     oscilloscope.o (.text.Oscilloscope_SendMeasurement)
                  00003308    00000092     tjc.o (.text.TJC_ParseByte)
                  0000339a    00000002     --HOLE-- [fill = 0]
                  0000339c    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_ADC_MOTOR_V_init)
                  0000342c    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_UART0_init)
                  000034bc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOER_A_init)
                  00003548    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000035d4    00000088     oscilloscope.o (.text.Oscilloscope_Init)
                  0000365c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000036de    00000002     --HOLE-- [fill = 0]
                  000036e0    00000080     motor.o (.text.MOTOR_Init)
                  00003760    0000007c     state_machine.o (.text.StateMachine_HandleDataSending)
                  000037dc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003858    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000038cc    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000038d0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003944    00000070     oscilloscope.o (.text.Oscilloscope_DMAComplete)
                  000039b4    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init)
                  00003a24    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003a94    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003afc    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003b62    00000002     --HOLE-- [fill = 0]
                  00003b64    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003bc6    00000062     libc.a : memset16.S.obj (.text:memset)
                  00003c28    0000005c     oscilloscope.o (.text.Oscilloscope_Start)
                  00003c84    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003ce0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00003d38    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003d90    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003de8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003e3e    00000002     --HOLE-- [fill = 0]
                  00003e40    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_QEI_0_init)
                  00003e94    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003ee6    00000002     --HOLE-- [fill = 0]
                  00003ee8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003f34    0000004c     time.o (.text.QEI_GetSpeed)
                  00003f80    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003fca    00000002     --HOLE-- [fill = 0]
                  00003fcc    00000048     adc.o (.text.ADC_Init)
                  00004014    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000405c    00000044     oscilloscope.o (.text.Oscilloscope_SingleTrigger)
                  000040a0    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000040e4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004124    00000040     key.o (.text.KEY_All_Init)
                  00004164    00000040     oscilloscope.o (.text.Oscilloscope_Stop)
                  000041a4    00000040     state_machine.o (.text.StateMachine_FrameReceivedCallback)
                  000041e4    00000040     usart.o (.text.UART_SendString)
                  00004224    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004264    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000042a4    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000042e4    0000003c            : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004320    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000435a    00000002     --HOLE-- [fill = 0]
                  0000435c    00000038     adc.o (.text.ADC_DMA_TransferComplete)
                  00004394    00000038     adc.o (.text.ADC_StartConversion)
                  000043cc    00000038     main.o (.text.DMA_IRQHandler)
                  00004404    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0000443c    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004474    00000036     libclang_rt.builtins.a : floatundisf.S.obj (.text.__floatundisf)
                  000044aa    00000002     --HOLE-- [fill = 0]
                  000044ac    00000034     time.o (.text.timer_init)
                  000044e0    00000032     libclang_rt.builtins.a : fixunssfsi.S.obj (.text.__fixunssfsi)
                  00004512    00000002     --HOLE-- [fill = 0]
                  00004514    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004544    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004574    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_A1_init)
                  000045a0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000045cc    00000028                            : floatunsisf.S.obj (.text.__floatunsisf)
                  000045f4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000461c    00000024     state_machine.o (.text.StateMachine_Init)
                  00004640    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004664    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00004688    00000024     delay.o (.text.delay_ms)
                  000046ac    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000046ce    00000002     --HOLE-- [fill = 0]
                  000046d0    00000020     oscilloscope.o (.text.Oscilloscope_SetTrigger)
                  000046f0    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000470e    00000002     --HOLE-- [fill = 0]
                  00004710    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000472c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004748    0000001c     delay.o (.text.delay_init)
                  00004764    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000477c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00004794    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  000047ac    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH2_init)
                  000047c4    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000047dc    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000047f0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00004804    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004816    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00004828    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004838    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00004848    00000010     delay.o (.text.SysTick_Handler)
                  00004858    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004868    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00004878    0000000e     key.o (.text.KEY_GetEvent)
                  00004886    00000002     --HOLE-- [fill = 0]
                  00004888    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004896    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000048a4    0000000c     adc.o (.text.ADC_GetLastResult)
                  000048b0    0000000c     adc.o (.text.ADC_IsConversionComplete)
                  000048bc    0000000c     oscilloscope.o (.text.Oscilloscope_SetCH1Scale)
                  000048c8    0000000c     time.o (.text.QEI_GetPosition)
                  000048d4    0000000c     usart.o (.text.UART0_IRQHandler)
                  000048e0    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000048ec    0000000c     delay.o (.text.get_tick_ms)
                  000048f8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004902    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000490c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000491c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004926    0000000a     tjc.o (.text.TJC_ParserInit)
                  00004930    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  0000493a    00000008     main.o (.text.TJC_FrameReceivedCallback)
                  00004942    00000002     --HOLE-- [fill = 0]
                  00004944    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000494c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004954    00000006     libc.a : exit.c.obj (.text:abort)
                  0000495a    00000004     time.o (.text.QEI_GetDirection)
                  0000495e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004962    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00004966    0000000a     --HOLE-- [fill = 0]

.cinit     0    00004eb0    00000048     
                  00004eb0    0000001d     (.cinit..data.load) [load image, compression = lzss]
                  00004ecd    00000003     --HOLE-- [fill = 0]
                  00004ed0    0000000c     (__TI_handler_table)
                  00004edc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004ee4    00000010     (__TI_cinit_table)
                  00004ef4    00000004     --HOLE-- [fill = 0]

.rodata    0    00004970    00000540     
                  00004970    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004a71    00000034     motor.o (.rodata.str1.12748093362806134514.1)
                  00004aa5    00000034     motor.o (.rodata.str1.7408246413643671320.1)
                  00004ad9    00000031     motor.o (.rodata.str1.5850567729483738290.1)
                  00004b0a    0000002e     oscilloscope.o (.rodata.str1.1187178666048312369.1)
                  00004b38    0000002c     oscilloscope.o (.rodata.str1.2343358154892508899.1)
                  00004b64    0000002b     state_machine.o (.rodata.str1.5499806488662909927.1)
                  00004b8f    00000027     usart.o (.rodata.str1.10308446891049622352.1)
                  00004bb6    00000002     ti_msp_dl_config.o (.rodata.gUSER_UART0ClockConfig)
                  00004bb8    00000026     main.o (.rodata.str1.8154729771448623357.4)
                  00004bde    00000024     motor.o (.rodata.str1.10718775090649846465.1)
                  00004c02    00000002     --HOLE-- [fill = 0]
                  00004c04    0000001f     main.o (.rodata.str1.18227636981041470289.4)
                  00004c23    00000001     --HOLE-- [fill = 0]
                  00004c24    0000001c     main.o (.rodata.str1.17100691992556644108.4)
                  00004c40    0000001b     main.o (.rodata.str1.15159059442110792349.1)
                  00004c5b    00000019     oscilloscope.o (.rodata.str1.5787482841568765340.1)
                  00004c74    00000018     usart.o (.rodata..L__const.UART_Init.dmaConfig)
                  00004c8c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00004ca4    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00004cbc    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH2Config)
                  00004cd4    00000017     oscilloscope.o (.rodata.str1.1459948170283282674.1)
                  00004ceb    00000016     oscilloscope.o (.rodata.str1.1780318205836774058.1)
                  00004d01    00000003     oscilloscope.o (.rodata.Port_End.end_bytes)
                  00004d04    00000014     ti_msp_dl_config.o (.rodata.gTIMER_A1TimerConfig)
                  00004d18    00000014     oscilloscope.o (.rodata.str1.15524357232992035370.1)
                  00004d2c    00000014     oscilloscope.o (.rodata.str1.17604906939224783023.1)
                  00004d40    00000014     oscilloscope.o (.rodata.str1.6915120742272157500.1)
                  00004d54    00000013     oscilloscope.o (.rodata.str1.14387147706737944075.4)
                  00004d67    00000001     --HOLE-- [fill = 0]
                  00004d68    00000013     oscilloscope.o (.rodata.str1.2956236276823147617.4)
                  00004d7b    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004d8c    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004d9d    00000003     send_info.o (.rodata.Port_End.end_bytes)
                  00004da0    00000010     state_machine.o (.rodata.cst16)
                  00004db0    00000010     oscilloscope.o (.rodata.str1.10319031187862147311.1)
                  00004dc0    00000010     oscilloscope.o (.rodata.str1.16236160138691104948.1)
                  00004dd0    0000000f     oscilloscope.o (.rodata.str1.16037326536637467922.1)
                  00004ddf    0000000f     send_info.o (.rodata.str1.16606331108790205631.1)
                  00004dee    0000000f     send_info.o (.rodata.str1.2450867416504314851.1)
                  00004dfd    0000000f     oscilloscope.o (.rodata.str1.4462293099754117331.1)
                  00004e0c    0000000e     send_info.o (.rodata.str1.13342906380623343076.1)
                  00004e1a    0000000d     oscilloscope.o (.rodata.str1.17501530167750222819.1)
                  00004e27    0000000d     send_info.o (.rodata.str1.6503431098811812287.1)
                  00004e34    0000000d     send_info.o (.rodata.str1.766284786160762975.1)
                  00004e41    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTOER_AClockConfig)
                  00004e44    0000000c     oscilloscope.o (.rodata..Lswitch.table.Oscilloscope_SendStatus)
                  00004e50    0000000a     ti_msp_dl_config.o (.rodata.gUSER_UART0Config)
                  00004e5a    00000002     --HOLE-- [fill = 0]
                  00004e5c    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTOER_AConfig)
                  00004e64    00000008     ti_msp_dl_config.o (.rodata.gUSER_ADC_MOTOR_VClockConfig)
                  00004e6c    00000008     ti_msp_dl_config.o (.rodata.gUSER_ADC_OSCILLOSCOPEClockConfig)
                  00004e74    00000008     oscilloscope.o (.rodata.str1.5953213059392086888.1)
                  00004e7c    00000007     oscilloscope.o (.rodata.str1.12606275727791414890.1)
                  00004e83    00000007     oscilloscope.o (.rodata.str1.3007264862663221617.1)
                  00004e8a    00000007     oscilloscope.o (.rodata.str1.8587496025437590917.1)
                  00004e91    00000006     oscilloscope.o (.rodata.str1.3789914365216800215.1)
                  00004e97    00000005     oscilloscope.o (.rodata.str1.15768169768950500062.1)
                  00004e9c    00000004     oscilloscope.o (.rodata.str1.3413135198783340469.1)
                  00004ea0    00000003     ti_msp_dl_config.o (.rodata.gTIMER_A1ClockConfig)
                  00004ea3    00000003     ti_msp_dl_config.o (.rodata.gUSER_QEI_0ClockConfig)
                  00004ea6    00000003     oscilloscope.o (.rodata.str1.14028305355893671857.1)
                  00004ea9    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000619     UNINITIALIZED
                  20200000    000003e8     (.common:uart_buffer)
                  202003e8    000000bc     (.common:gTIMER_A1Backup)
                  202004a4    000000a0     (.common:gUSER_QEI_0Backup)
                  20200544    00000040     usart.o (.bss.rxProcessBuffer)
                  20200584    00000028     (.common:userKey)
                  202005ac    00000028     (.common:userKey1)
                  202005d4    00000025     (.common:tjc_parser)
                  202005f9    00000020     (.common:gRxPacket)

.data      0    20200620    0000057c     UNINITIALIZED
                  20200620    000004d8     oscilloscope.o (.data.osc_config)
                  20200af8    00000028     state_machine.o (.data.sm_state)
                  20200b20    00000008     main.o (.data.allKeys)
                  20200b28    00000004     adc.o (.data.ADC_DMA_TransferComplete.dma_complete_count)
                  20200b2c    00000004     adc.o (.data.ADC_StartConversion.start_count)
                  20200b30    00000004     state_machine.o (.data.StateMachine_HandleDataSending.last_serial_time)
                  20200b34    00000004     state_machine.o (.data.StateMachine_HandleDataSending.last_status_time)
                  20200b38    00000004     state_machine.o (.data.StateMachine_HandleMotorSpeedIncrease.motor_speed)
                  20200b3c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200b40    00000004     adc.o (.data.adc_result)
                  20200b44    00000004     main.o (.data.adc_value)
                  20200b48    00000004     usart.o (.data.bytesProcessedCount)
                  20200b4c    00000004     main.o (.data.dma_ch1_interrupt_count)
                  20200b50    00000004     time.o (.data.encoderSpeed)
                  20200b54    00000004     send_info.o (.data.encoder_position)
                  20200b58    00000004     send_info.o (.data.encoder_speed)
                  20200b5c    00000004     delay.o (.data.g_tick_ms)
                  20200b60    00000004     usart.o (.data.lastDmaTransferSize)
                  20200b64    00000004     time.o (.data.lastPosition)
                  20200b68    00000004     time.o (.data.lastTime)
                  20200b6c    00000004     main.o (.data.last_adc_sample_time)
                  20200b70    00000004     main.o (.data.last_key_scan_time)
                  20200b74    00000004     send_info.o (.data.last_position)
                  20200b78    00000004     main.o (.data.led_blink_time)
                  20200b7c    00000004     motor.o (.data.motor_speed)
                  20200b80    00000004     oscilloscope.o (.data.osc_dma_interrupt_count)
                  20200b84    00000004     motor.o (.data.period)
                  20200b88    00000004     usart.o (.data.stableCount)
                  20200b8c    00000002     adc.o (.data.adc_dma_buffer)
                  20200b8e    00000002     main.o (.data.prevKeyEvent)
                  20200b90    00000002     usart.o (.data.rxAvailableBytes)
                  20200b92    00000002     usart.o (.data.rxProcessIndex)
                  20200b94    00000001     main.o (.data.Handle_ADC_Sampling_NonBlocking.adc_state)
                  20200b95    00000001     adc.o (.data.adc_conversion_complete)
                  20200b96    00000001     send_info.o (.data.encoder_direction)
                  20200b97    00000001     usart.o (.data.gRxComplete)
                  20200b98    00000001     main.o (.data.key_encoder_reset)
                  20200b99    00000001     main.o (.data.led_blink_active)
                  20200b9a    00000001     motor.o (.data.motor_init)
                  20200b9b    00000001     state_machine.o (.data.oscilloscope_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       main.o                         532     124       1113   
       ti_msp_dl_config.o             1072    137       348    
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1610    453       1461   
                                                               
    .\App\
       oscilloscope.o                 3076    395       1244   
       state_machine.o                840     59        53     
       send_info.o                    252     73        13     
       tjc.o                          156     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         4324    527       1347   
                                                               
    .\BSP\
       usart.o                        1388    63        113    
       key.o                          820     0         0      
       motor.o                        608     189       9      
       adc.o                          208     0         15     
       time.o                         144     0         12     
       delay.o                        92      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         3260    252       153    
                                                               
    F:/Ti/ccs/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         828     0         0      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       e_sqrtf.c.obj                  160     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5798    291       4      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatundisf.S.obj              54      0         0      
       fixunssfsi.S.obj               50      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2746    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       65        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   18570   1588      3477   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004ee4 records: 2, size/record: 8, table size: 16
	.data: load addr=00004eb0, load size=0000001d bytes, run addr=20200620, run size=0000057c bytes, compression=lzss
	.bss: load addr=00004edc, load size=00000008 bytes, run addr=20200000, run size=00000619 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004ed0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001ccd     0000490c     0000490a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                  
-------   ----                                  
00001433  ADC0_IRQHandler                       
00001433  ADC1_IRQHandler                       
0000435d  ADC_DMA_TransferComplete              
000048a5  ADC_GetLastResult                     
00003fcd  ADC_Init                              
000048b1  ADC_IsConversionComplete              
00004395  ADC_StartConversion                   
00001433  AES_IRQHandler                        
000038cc  C$$EXIT                               
00001433  CANFD0_IRQHandler                     
00001433  DAC0_IRQHandler                       
000040e5  DL_ADC12_setClockConfig               
000048f9  DL_Common_delayCycles                 
00003ee9  DL_DMA_initChannel                    
000026e1  DL_Timer_initFourCCPWMMode            
000029d1  DL_Timer_initTimerMode                
00004711  DL_Timer_setCaptCompUpdateMethod      
00004765  DL_Timer_setCaptureCompareOutCtl      
00004829  DL_Timer_setCaptureCompareValue       
0000472d  DL_Timer_setClockConfig               
00004015  DL_UART_init                          
00004805  DL_UART_setClockConfig                
000043cd  DMA_IRQHandler                        
00001433  Default_Handler                       
00001433  GROUP0_IRQHandler                     
00001433  GROUP1_IRQHandler                     
000038cd  HOSTexit                              
00001433  HardFault_Handler                     
00001433  I2C0_IRQHandler                       
00001433  I2C1_IRQHandler                       
000028e1  KET_Event_NonBlocking                 
00004125  KEY_All_Init                          
00004879  KEY_GetEvent                          
0000114d  KEY_ScanMultiple                      
000036e1  MOTOR_Init                            
00001433  NMI_Handler                           
00003945  Oscilloscope_DMAComplete              
000035d5  Oscilloscope_Init                     
00000a91  Oscilloscope_MeasureWaveform          
0000225d  Oscilloscope_ReadRealADC              
00003275  Oscilloscope_SendMeasurement          
00001e61  Oscilloscope_SendStatus               
00000e5d  Oscilloscope_SendWaveformData         
000048bd  Oscilloscope_SetCH1Scale              
000046d1  Oscilloscope_SetTrigger               
0000405d  Oscilloscope_SingleTrigger            
00003c29  Oscilloscope_Start                    
00004165  Oscilloscope_Stop                     
00001433  PendSV_Handler                        
0000495b  QEI_GetDirection                      
000048c9  QEI_GetPosition                       
00003f35  QEI_GetSpeed                          
00001433  RTC_IRQHandler                        
0000495f  Reset_Handler                         
00001433  SPI0_IRQHandler                       
00001433  SPI1_IRQHandler                       
00001433  SVC_Handler                           
0000477d  SYSCFG_DL_DMA_CH0_init                
00004795  SYSCFG_DL_DMA_CH1_init                
000047ad  SYSCFG_DL_DMA_CH2_init                
00004839  SYSCFG_DL_DMA_init                    
00003ce1  SYSCFG_DL_GPIO_init                   
000034bd  SYSCFG_DL_PWM_MOTOER_A_init           
00004515  SYSCFG_DL_SYSCTL_init                 
00004575  SYSCFG_DL_TIMER_A1_init               
0000339d  SYSCFG_DL_USER_ADC_MOTOR_V_init       
000039b5  SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init  
00003e41  SYSCFG_DL_USER_QEI_0_init             
0000342d  SYSCFG_DL_USER_UART0_init             
000040a1  SYSCFG_DL_init                        
00003a25  SYSCFG_DL_initPower                   
000027e5  Send_Debug_Info_Port                  
000041a5  StateMachine_FrameReceivedCallback    
00003761  StateMachine_HandleDataSending        
00001fdd  StateMachine_HandleOscilloscopeControl
0000461d  StateMachine_Init                     
00002391  StateMachine_Process                  
00004849  SysTick_Handler                       
00001433  TIMA0_IRQHandler                      
00001433  TIMA1_IRQHandler                      
00001433  TIMG0_IRQHandler                      
00001433  TIMG12_IRQHandler                     
00001433  TIMG6_IRQHandler                      
00001433  TIMG7_IRQHandler                      
00001433  TIMG8_IRQHandler                      
0000493b  TJC_FrameReceivedCallback             
00003309  TJC_ParseByte                         
00004927  TJC_ParserInit                        
000048d5  UART0_IRQHandler                      
00001433  UART1_IRQHandler                      
00001433  UART2_IRQHandler                      
00001433  UART3_IRQHandler                      
00002f11  UART_Init                             
00001435  UART_ProcessReceivedData              
00002fe5  UART_ProcessTJCData                   
00002c81  UART_SendBuffer                       
000041e5  UART_SendString                       
20208000  __STACK_END                           
00000200  __STACK_SIZE                          
00000000  __TI_ATRegion0_region_sz              
00000000  __TI_ATRegion0_src_addr               
00000000  __TI_ATRegion0_trg_addr               
00000000  __TI_ATRegion1_region_sz              
00000000  __TI_ATRegion1_src_addr               
00000000  __TI_ATRegion1_trg_addr               
00000000  __TI_ATRegion2_region_sz              
00000000  __TI_ATRegion2_src_addr               
00000000  __TI_ATRegion2_trg_addr               
00004ee4  __TI_CINIT_Base                       
00004ef4  __TI_CINIT_Limit                      
00004ef4  __TI_CINIT_Warm                       
00004ed0  __TI_Handler_Table_Base               
00004edc  __TI_Handler_Table_Limit              
000042e5  __TI_auto_init_nobinit_nopinit        
000037dd  __TI_decompress_lzss                  
00004817  __TI_decompress_none                  
00003d39  __TI_ltoa                             
ffffffff  __TI_pprof_out_hndl                   
000000c1  __TI_printfi                          
ffffffff  __TI_prof_data_size                   
ffffffff  __TI_prof_data_start                  
00000000  __TI_static_base__                    
00004869  __TI_zero_init                        
00001cd7  __adddf3                              
00002e43  __addsf3                              
00004970  __aeabi_ctype_table_                  
00004970  __aeabi_ctype_table_C                 
000038d1  __aeabi_d2f                           
00003f81  __aeabi_d2iz                          
00001cd7  __aeabi_dadd                          
00003b65  __aeabi_dcmpeq                        
00003ba1  __aeabi_dcmpge                        
00003bb5  __aeabi_dcmpgt                        
00003b8d  __aeabi_dcmple                        
00003b79  __aeabi_dcmplt                        
000025d5  __aeabi_ddiv                          
00002ab9  __aeabi_dmul                          
00001ccd  __aeabi_dsub                          
20200b3c  __aeabi_errno                         
00004945  __aeabi_errno_addr                    
00004265  __aeabi_f2d                           
00004405  __aeabi_f2iz                          
000044e1  __aeabi_f2uiz                         
00002e43  __aeabi_fadd                          
0000365d  __aeabi_fdiv                          
00003549  __aeabi_fmul                          
00002e39  __aeabi_fsub                          
000045a1  __aeabi_i2d                           
00003de9  __aeabi_idiv                          
00001e5f  __aeabi_idiv0                         
00003de9  __aeabi_idivmod                       
00003137  __aeabi_ldiv0                         
000046f1  __aeabi_llsl                          
00004665  __aeabi_lmul                          
000048e1  __aeabi_memclr                        
000048e1  __aeabi_memclr4                       
000048e1  __aeabi_memclr8                       
0000494d  __aeabi_memcpy                        
0000494d  __aeabi_memcpy4                       
0000494d  __aeabi_memcpy8                       
00004889  __aeabi_memset                        
00004889  __aeabi_memset4                       
00004889  __aeabi_memset8                       
00004641  __aeabi_ui2d                          
000045cd  __aeabi_ui2f                          
00004225  __aeabi_uidiv                         
00004225  __aeabi_uidivmod                      
00004475  __aeabi_ul2f                          
000047dd  __aeabi_uldivmod                      
000046f1  __ashldi3                             
ffffffff  __binit__                             
00003a95  __cmpdf2                              
000025d5  __divdf3                              
0000365d  __divsf3                              
00003a95  __eqdf2                               
00004265  __extendsfdf2                         
00003f81  __fixdfsi                             
00004405  __fixsfsi                             
000044e1  __fixunssfsi                          
000045a1  __floatsidf                           
00004475  __floatundisf                         
00004641  __floatunsidf                         
000045cd  __floatunsisf                         
00003859  __gedf2                               
00003859  __gtdf2                               
00003a95  __ledf2                               
00003a95  __ltdf2                               
UNDEFED   __mpu_init                            
00002ab9  __muldf3                              
00004665  __muldi3                              
00004321  __muldsi3                             
00003549  __mulsf3                              
00003a95  __nedf2                               
20207e00  __stack                               
20200000  __start___llvm_prf_bits               
20200000  __start___llvm_prf_cnts               
20200000  __stop___llvm_prf_bits                
20200000  __stop___llvm_prf_cnts                
00001ccd  __subdf3                              
00002e39  __subsf3                              
000038d1  __truncdfsf2                          
00003095  __udivmoddi4                          
000045f5  _c_int00_noargs                       
UNDEFED   _system_post_cinit                    
00004963  _system_pre_init                      
00004955  abort                                 
20200b44  adc_value                             
20200b20  allKeys                               
000042a5  atoi                                  
ffffffff  binit                                 
00004749  delay_init                            
00004689  delay_ms                              
20200b4c  dma_ch1_interrupt_count               
20200b96  encoder_direction                     
20200b54  encoder_position                      
20200b58  encoder_speed                         
00003c85  frexp                                 
00003c85  frexpl                                
20200b97  gRxComplete                           
202005f9  gRxPacket                             
202003e8  gTIMER_A1Backup                       
202004a4  gUSER_QEI_0Backup                     
000048ed  get_tick_ms                           
00000000  interruptVectors                      
20200b98  key_encoder_reset                     
20200b74  last_position                         
00002d61  ldexp                                 
00002d61  ldexpl                                
00002b9d  main                                  
000046ad  memccpy                               
000031d9  memcpy                                
00003bc7  memset                                
20200b9a  motor_init                            
20200b7c  motor_speed                           
20200620  osc_config                            
20200b80  osc_dma_interrupt_count               
20200b84  period                                
20200b8e  prevKeyEvent                          
00002d61  scalbn                                
00002d61  scalbnl                               
00001911  set_motor_speed                       
20200af8  sm_state                              
0000443d  sprintf                               
00003139  sqrtf                                 
000044ad  timer_init                            
202005d4  tjc_parser                            
20200000  uart_buffer                           
20200584  userKey                               
202005ac  userKey1                              
00004859  wcslen                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                  
-------   ----                                  
00000000  __TI_ATRegion0_region_sz              
00000000  __TI_ATRegion0_src_addr               
00000000  __TI_ATRegion0_trg_addr               
00000000  __TI_ATRegion1_region_sz              
00000000  __TI_ATRegion1_src_addr               
00000000  __TI_ATRegion1_trg_addr               
00000000  __TI_ATRegion2_region_sz              
00000000  __TI_ATRegion2_src_addr               
00000000  __TI_ATRegion2_trg_addr               
00000000  __TI_static_base__                    
00000000  interruptVectors                      
000000c1  __TI_printfi                          
00000200  __STACK_SIZE                          
00000a91  Oscilloscope_MeasureWaveform          
00000e5d  Oscilloscope_SendWaveformData         
0000114d  KEY_ScanMultiple                      
00001433  ADC0_IRQHandler                       
00001433  ADC1_IRQHandler                       
00001433  AES_IRQHandler                        
00001433  CANFD0_IRQHandler                     
00001433  DAC0_IRQHandler                       
00001433  Default_Handler                       
00001433  GROUP0_IRQHandler                     
00001433  GROUP1_IRQHandler                     
00001433  HardFault_Handler                     
00001433  I2C0_IRQHandler                       
00001433  I2C1_IRQHandler                       
00001433  NMI_Handler                           
00001433  PendSV_Handler                        
00001433  RTC_IRQHandler                        
00001433  SPI0_IRQHandler                       
00001433  SPI1_IRQHandler                       
00001433  SVC_Handler                           
00001433  TIMA0_IRQHandler                      
00001433  TIMA1_IRQHandler                      
00001433  TIMG0_IRQHandler                      
00001433  TIMG12_IRQHandler                     
00001433  TIMG6_IRQHandler                      
00001433  TIMG7_IRQHandler                      
00001433  TIMG8_IRQHandler                      
00001433  UART1_IRQHandler                      
00001433  UART2_IRQHandler                      
00001433  UART3_IRQHandler                      
00001435  UART_ProcessReceivedData              
00001911  set_motor_speed                       
00001ccd  __aeabi_dsub                          
00001ccd  __subdf3                              
00001cd7  __adddf3                              
00001cd7  __aeabi_dadd                          
00001e5f  __aeabi_idiv0                         
00001e61  Oscilloscope_SendStatus               
00001fdd  StateMachine_HandleOscilloscopeControl
0000225d  Oscilloscope_ReadRealADC              
00002391  StateMachine_Process                  
000025d5  __aeabi_ddiv                          
000025d5  __divdf3                              
000026e1  DL_Timer_initFourCCPWMMode            
000027e5  Send_Debug_Info_Port                  
000028e1  KET_Event_NonBlocking                 
000029d1  DL_Timer_initTimerMode                
00002ab9  __aeabi_dmul                          
00002ab9  __muldf3                              
00002b9d  main                                  
00002c81  UART_SendBuffer                       
00002d61  ldexp                                 
00002d61  ldexpl                                
00002d61  scalbn                                
00002d61  scalbnl                               
00002e39  __aeabi_fsub                          
00002e39  __subsf3                              
00002e43  __addsf3                              
00002e43  __aeabi_fadd                          
00002f11  UART_Init                             
00002fe5  UART_ProcessTJCData                   
00003095  __udivmoddi4                          
00003137  __aeabi_ldiv0                         
00003139  sqrtf                                 
000031d9  memcpy                                
00003275  Oscilloscope_SendMeasurement          
00003309  TJC_ParseByte                         
0000339d  SYSCFG_DL_USER_ADC_MOTOR_V_init       
0000342d  SYSCFG_DL_USER_UART0_init             
000034bd  SYSCFG_DL_PWM_MOTOER_A_init           
00003549  __aeabi_fmul                          
00003549  __mulsf3                              
000035d5  Oscilloscope_Init                     
0000365d  __aeabi_fdiv                          
0000365d  __divsf3                              
000036e1  MOTOR_Init                            
00003761  StateMachine_HandleDataSending        
000037dd  __TI_decompress_lzss                  
00003859  __gedf2                               
00003859  __gtdf2                               
000038cc  C$$EXIT                               
000038cd  HOSTexit                              
000038d1  __aeabi_d2f                           
000038d1  __truncdfsf2                          
00003945  Oscilloscope_DMAComplete              
000039b5  SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init  
00003a25  SYSCFG_DL_initPower                   
00003a95  __cmpdf2                              
00003a95  __eqdf2                               
00003a95  __ledf2                               
00003a95  __ltdf2                               
00003a95  __nedf2                               
00003b65  __aeabi_dcmpeq                        
00003b79  __aeabi_dcmplt                        
00003b8d  __aeabi_dcmple                        
00003ba1  __aeabi_dcmpge                        
00003bb5  __aeabi_dcmpgt                        
00003bc7  memset                                
00003c29  Oscilloscope_Start                    
00003c85  frexp                                 
00003c85  frexpl                                
00003ce1  SYSCFG_DL_GPIO_init                   
00003d39  __TI_ltoa                             
00003de9  __aeabi_idiv                          
00003de9  __aeabi_idivmod                       
00003e41  SYSCFG_DL_USER_QEI_0_init             
00003ee9  DL_DMA_initChannel                    
00003f35  QEI_GetSpeed                          
00003f81  __aeabi_d2iz                          
00003f81  __fixdfsi                             
00003fcd  ADC_Init                              
00004015  DL_UART_init                          
0000405d  Oscilloscope_SingleTrigger            
000040a1  SYSCFG_DL_init                        
000040e5  DL_ADC12_setClockConfig               
00004125  KEY_All_Init                          
00004165  Oscilloscope_Stop                     
000041a5  StateMachine_FrameReceivedCallback    
000041e5  UART_SendString                       
00004225  __aeabi_uidiv                         
00004225  __aeabi_uidivmod                      
00004265  __aeabi_f2d                           
00004265  __extendsfdf2                         
000042a5  atoi                                  
000042e5  __TI_auto_init_nobinit_nopinit        
00004321  __muldsi3                             
0000435d  ADC_DMA_TransferComplete              
00004395  ADC_StartConversion                   
000043cd  DMA_IRQHandler                        
00004405  __aeabi_f2iz                          
00004405  __fixsfsi                             
0000443d  sprintf                               
00004475  __aeabi_ul2f                          
00004475  __floatundisf                         
000044ad  timer_init                            
000044e1  __aeabi_f2uiz                         
000044e1  __fixunssfsi                          
00004515  SYSCFG_DL_SYSCTL_init                 
00004575  SYSCFG_DL_TIMER_A1_init               
000045a1  __aeabi_i2d                           
000045a1  __floatsidf                           
000045cd  __aeabi_ui2f                          
000045cd  __floatunsisf                         
000045f5  _c_int00_noargs                       
0000461d  StateMachine_Init                     
00004641  __aeabi_ui2d                          
00004641  __floatunsidf                         
00004665  __aeabi_lmul                          
00004665  __muldi3                              
00004689  delay_ms                              
000046ad  memccpy                               
000046d1  Oscilloscope_SetTrigger               
000046f1  __aeabi_llsl                          
000046f1  __ashldi3                             
00004711  DL_Timer_setCaptCompUpdateMethod      
0000472d  DL_Timer_setClockConfig               
00004749  delay_init                            
00004765  DL_Timer_setCaptureCompareOutCtl      
0000477d  SYSCFG_DL_DMA_CH0_init                
00004795  SYSCFG_DL_DMA_CH1_init                
000047ad  SYSCFG_DL_DMA_CH2_init                
000047dd  __aeabi_uldivmod                      
00004805  DL_UART_setClockConfig                
00004817  __TI_decompress_none                  
00004829  DL_Timer_setCaptureCompareValue       
00004839  SYSCFG_DL_DMA_init                    
00004849  SysTick_Handler                       
00004859  wcslen                                
00004869  __TI_zero_init                        
00004879  KEY_GetEvent                          
00004889  __aeabi_memset                        
00004889  __aeabi_memset4                       
00004889  __aeabi_memset8                       
000048a5  ADC_GetLastResult                     
000048b1  ADC_IsConversionComplete              
000048bd  Oscilloscope_SetCH1Scale              
000048c9  QEI_GetPosition                       
000048d5  UART0_IRQHandler                      
000048e1  __aeabi_memclr                        
000048e1  __aeabi_memclr4                       
000048e1  __aeabi_memclr8                       
000048ed  get_tick_ms                           
000048f9  DL_Common_delayCycles                 
00004927  TJC_ParserInit                        
0000493b  TJC_FrameReceivedCallback             
00004945  __aeabi_errno_addr                    
0000494d  __aeabi_memcpy                        
0000494d  __aeabi_memcpy4                       
0000494d  __aeabi_memcpy8                       
00004955  abort                                 
0000495b  QEI_GetDirection                      
0000495f  Reset_Handler                         
00004963  _system_pre_init                      
00004970  __aeabi_ctype_table_                  
00004970  __aeabi_ctype_table_C                 
00004ed0  __TI_Handler_Table_Base               
00004edc  __TI_Handler_Table_Limit              
00004ee4  __TI_CINIT_Base                       
00004ef4  __TI_CINIT_Limit                      
00004ef4  __TI_CINIT_Warm                       
20200000  __start___llvm_prf_bits               
20200000  __start___llvm_prf_cnts               
20200000  __stop___llvm_prf_bits                
20200000  __stop___llvm_prf_cnts                
20200000  uart_buffer                           
202003e8  gTIMER_A1Backup                       
202004a4  gUSER_QEI_0Backup                     
20200584  userKey                               
202005ac  userKey1                              
202005d4  tjc_parser                            
202005f9  gRxPacket                             
20200620  osc_config                            
20200af8  sm_state                              
20200b20  allKeys                               
20200b3c  __aeabi_errno                         
20200b44  adc_value                             
20200b4c  dma_ch1_interrupt_count               
20200b54  encoder_position                      
20200b58  encoder_speed                         
20200b74  last_position                         
20200b7c  motor_speed                           
20200b80  osc_dma_interrupt_count               
20200b84  period                                
20200b8e  prevKeyEvent                          
20200b96  encoder_direction                     
20200b97  gRxComplete                           
20200b98  key_encoder_reset                     
20200b9a  motor_init                            
20207e00  __stack                               
20208000  __STACK_END                           
ffffffff  __TI_pprof_out_hndl                   
ffffffff  __TI_prof_data_size                   
ffffffff  __TI_prof_data_start                  
ffffffff  __binit__                             
ffffffff  binit                                 
UNDEFED   __mpu_init                            
UNDEFED   _system_post_cinit                    

[250 symbols]
