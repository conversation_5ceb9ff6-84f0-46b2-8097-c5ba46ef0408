******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 29 10:50:15 2025

OUTPUT FILE NAME:   <key.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000044fd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004e48  0001b1b8  R  X
  SRAM                  20200000   00008000  00000d8d  00007273  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004e48   00004e48    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000047f0   000047f0    r-x .text
  000048b0    000048b0    00000550   00000550    r-- .rodata
  00004e00    00004e00    00000048   00000048    r-- .cinit
20200000    20200000    00000b94   00000000    rw-
  20200000    20200000    00000619   00000000    rw- .bss
  20200620    20200620    00000574   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000047f0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000003cc     oscilloscope.o (.text.Oscilloscope_MeasureWaveform)
                  00000e5c    000002f0     send_info.o (.text.Oscilloscope_SendWaveformData)
                  0000114c    000002e6     key.o (.text.KEY_ScanMultiple)
                  00001432    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001434    000002bc     usart.o (.text.UART_ProcessReceivedData)
                  000016f0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001910    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001aec    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001c7e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001c80    0000017c     send_info.o (.text.Oscilloscope_SendStatus)
                  00001dfc    0000015c     motor.o (.text.set_motor_speed)
                  00001f58    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002094    00000134     oscilloscope.o (.text.Oscilloscope_ReadRealADC)
                  000021c8    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000022e8    0000011c     state_machine.o (.text.StateMachine_Process)
                  00002404    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002510    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002614    000000fc     send_info.o (.text.Send_Debug_Info_Port)
                  00002710    000000f4     state_machine.o (.text.StateMachine_HandleOscilloscopeControl)
                  00002804    000000f0     main.o (.text.KET_Event_NonBlocking)
                  000028f4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000029dc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002ac0    000000e4     main.o (.text.main)
                  00002ba4    000000e0     usart.o (.text.UART_SendBuffer)
                  00002c84    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002d5c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002e34    000000d4     usart.o (.text.UART_Init)
                  00002f08    000000b0     usart.o (.text.UART_ProcessTJCData)
                  00002fb8    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000305a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000305c    000000a0     libc.a : e_sqrtf.c.obj (.text.sqrtf)
                  000030fc    0000009a            : memcpy16.S.obj (.text:memcpy)
                  00003196    00000002     --HOLE-- [fill = 0]
                  00003198    00000094     send_info.o (.text.Oscilloscope_SendMeasurement)
                  0000322c    00000092     tjc.o (.text.TJC_ParseByte)
                  000032be    00000002     --HOLE-- [fill = 0]
                  000032c0    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_ADC_MOTOR_V_init)
                  00003350    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_UART0_init)
                  000033e0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOER_A_init)
                  0000346c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000034f8    00000082                            : divsf3.S.obj (.text.__divsf3)
                  0000357a    00000002     --HOLE-- [fill = 0]
                  0000357c    00000080     motor.o (.text.MOTOR_Init)
                  000035fc    0000007c     state_machine.o (.text.StateMachine_HandleDataSending)
                  00003678    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000036f4    00000074     oscilloscope.o (.text.Oscilloscope_Init)
                  00003768    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000037dc    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000037e0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003854    00000070     oscilloscope.o (.text.Oscilloscope_DMAComplete)
                  000038c4    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init)
                  00003934    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000039a4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003a0c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003a72    00000002     --HOLE-- [fill = 0]
                  00003a74    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003ad6    00000062     libc.a : memset16.S.obj (.text:memset)
                  00003b38    0000005c     oscilloscope.o (.text.Oscilloscope_Start)
                  00003b94    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003bf0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00003c48    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003ca0    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003cf8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003d4e    00000002     --HOLE-- [fill = 0]
                  00003d50    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_USER_QEI_0_init)
                  00003da4    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003df6    00000002     --HOLE-- [fill = 0]
                  00003df8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00003e44    0000004c     time.o (.text.QEI_GetSpeed)
                  00003e90    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003eda    00000002     --HOLE-- [fill = 0]
                  00003edc    00000048     adc.o (.text.ADC_Init)
                  00003f24    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003f6c    00000044     oscilloscope.o (.text.Oscilloscope_SingleTrigger)
                  00003fb0    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003ff4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004034    00000040     key.o (.text.KEY_All_Init)
                  00004074    00000040     state_machine.o (.text.StateMachine_FrameReceivedCallback)
                  000040b4    00000040     usart.o (.text.UART_SendString)
                  000040f4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004134    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004174    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000041b4    0000003c            : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000041f0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000422a    00000002     --HOLE-- [fill = 0]
                  0000422c    00000038     adc.o (.text.ADC_DMA_TransferComplete)
                  00004264    00000038     adc.o (.text.ADC_StartConversion)
                  0000429c    00000038     main.o (.text.DMA_IRQHandler)
                  000042d4    00000038     oscilloscope.o (.text.Oscilloscope_Stop)
                  0000430c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004344    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  0000437c    00000036     libclang_rt.builtins.a : floatundisf.S.obj (.text.__floatundisf)
                  000043b2    00000002     --HOLE-- [fill = 0]
                  000043b4    00000034     time.o (.text.timer_init)
                  000043e8    00000032     libclang_rt.builtins.a : fixunssfsi.S.obj (.text.__fixunssfsi)
                  0000441a    00000002     --HOLE-- [fill = 0]
                  0000441c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000444c    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  0000447c    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_A1_init)
                  000044a8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000044d4    00000028                            : floatunsisf.S.obj (.text.__floatunsisf)
                  000044fc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004524    00000024     oscilloscope.o (.text.Oscilloscope_ForceTrigger)
                  00004548    00000024     state_machine.o (.text.StateMachine_Init)
                  0000456c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00004590    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000045b4    00000024     delay.o (.text.delay_ms)
                  000045d8    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000045fa    00000002     --HOLE-- [fill = 0]
                  000045fc    00000020     oscilloscope.o (.text.Oscilloscope_SetTrigger)
                  0000461c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000463a    00000002     --HOLE-- [fill = 0]
                  0000463c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00004658    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004674    0000001c     oscilloscope.o (.text.Oscilloscope_ClearWaveform)
                  00004690    0000001c     delay.o (.text.delay_init)
                  000046ac    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000046c4    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  000046dc    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  000046f4    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH2_init)
                  0000470c    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00004724    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004738    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000474c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000475e    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00004770    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004780    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00004790    00000010     delay.o (.text.SysTick_Handler)
                  000047a0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000047b0    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000047c0    0000000e     key.o (.text.KEY_GetEvent)
                  000047ce    00000002     --HOLE-- [fill = 0]
                  000047d0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000047de    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000047ec    0000000c     adc.o (.text.ADC_GetLastResult)
                  000047f8    0000000c     adc.o (.text.ADC_IsConversionComplete)
                  00004804    0000000c     time.o (.text.QEI_GetPosition)
                  00004810    0000000c     usart.o (.text.UART0_IRQHandler)
                  0000481c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00004828    0000000c     delay.o (.text.get_tick_ms)
                  00004834    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000483e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004848    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004858    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004862    0000000a     tjc.o (.text.TJC_ParserInit)
                  0000486c    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00004876    00000008     main.o (.text.TJC_FrameReceivedCallback)
                  0000487e    00000002     --HOLE-- [fill = 0]
                  00004880    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004888    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004890    00000006     libc.a : exit.c.obj (.text:abort)
                  00004896    00000004     time.o (.text.QEI_GetDirection)
                  0000489a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000489e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000048a2    0000000e     --HOLE-- [fill = 0]

.cinit     0    00004e00    00000048     
                  00004e00    0000001d     (.cinit..data.load) [load image, compression = lzss]
                  00004e1d    00000003     --HOLE-- [fill = 0]
                  00004e20    0000000c     (__TI_handler_table)
                  00004e2c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004e34    00000010     (__TI_cinit_table)
                  00004e44    00000004     --HOLE-- [fill = 0]

.rodata    0    000048b0    00000550     
                  000048b0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000049b1    0000002e     oscilloscope.o (.rodata.str1.3789914365216800215.1)
                  000049df    0000002c     oscilloscope.o (.rodata.str1.17501530167750222819.1)
                  00004a0b    0000002b     state_machine.o (.rodata.str1.5499806488662909927.1)
                  00004a36    00000027     usart.o (.rodata.str1.10308446891049622352.1)
                  00004a5d    00000003     send_info.o (.rodata.Port_End.end_bytes)
                  00004a60    00000026     main.o (.rodata.str1.8154729771448623357.4)
                  00004a86    00000024     motor.o (.rodata.str1.5850567729483738290.1)
                  00004aaa    00000020     state_machine.o (.rodata.str1.17285445957577556728.1)
                  00004aca    00000002     ti_msp_dl_config.o (.rodata.gUSER_UART0ClockConfig)
                  00004acc    0000001f     main.o (.rodata.str1.18227636981041470289.4)
                  00004aeb    00000001     --HOLE-- [fill = 0]
                  00004aec    0000001c     main.o (.rodata.str1.17100691992556644108.4)
                  00004b08    0000001b     state_machine.o (.rodata.str1.10278809154692732284.4)
                  00004b23    0000001b     main.o (.rodata.str1.15159059442110792349.1)
                  00004b3e    00000019     send_info.o (.rodata.str1.5279943184275334214.1)
                  00004b57    00000001     --HOLE-- [fill = 0]
                  00004b58    00000018     usart.o (.rodata..L__const.UART_Init.dmaConfig)
                  00004b70    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00004b88    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00004ba0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH2Config)
                  00004bb8    00000018     state_machine.o (.rodata.str1.3855662077583193624.4)
                  00004bd0    00000017     send_info.o (.rodata.str1.15238459376144547403.1)
                  00004be7    00000016     send_info.o (.rodata.str1.12343814962941690147.1)
                  00004bfd    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTOER_AClockConfig)
                  00004c00    00000015     state_machine.o (.rodata.str1.12802625103307508707.4)
                  00004c15    00000003     ti_msp_dl_config.o (.rodata.gTIMER_A1ClockConfig)
                  00004c18    00000015     state_machine.o (.rodata.str1.13712074210919037681.4)
                  00004c2d    00000003     ti_msp_dl_config.o (.rodata.gUSER_QEI_0ClockConfig)
                  00004c30    00000015     state_machine.o (.rodata.str1.14045022949785916042.4)
                  00004c45    00000003     send_info.o (.rodata.str1.14198352196226650032.1)
                  00004c48    00000015     state_machine.o (.rodata.str1.14502826223124610429.4)
                  00004c5d    00000003     --HOLE-- [fill = 0]
                  00004c60    00000014     ti_msp_dl_config.o (.rodata.gTIMER_A1TimerConfig)
                  00004c74    00000014     send_info.o (.rodata.str1.14536835306665630867.1)
                  00004c88    00000014     send_info.o (.rodata.str1.17094292589337375441.1)
                  00004c9c    00000014     send_info.o (.rodata.str1.6049280976157928166.1)
                  00004cb0    00000013     send_info.o (.rodata.str1.12854150023414117050.4)
                  00004cc3    00000001     --HOLE-- [fill = 0]
                  00004cc4    00000013     send_info.o (.rodata.str1.5883603548910157963.4)
                  00004cd7    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004ce8    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004cf9    00000010     send_info.o (.rodata.str1.2617261163612467819.1)
                  00004d09    00000010     send_info.o (.rodata.str1.8500294019303210070.1)
                  00004d19    0000000f     send_info.o (.rodata.str1.16606331108790205631.1)
                  00004d28    0000000f     state_machine.o (.rodata.str1.16700323032500373959.4)
                  00004d37    0000000f     send_info.o (.rodata.str1.17229586732647165751.1)
                  00004d46    0000000f     send_info.o (.rodata.str1.2450867416504314851.1)
                  00004d55    0000000f     send_info.o (.rodata.str1.3760426283636117217.1)
                  00004d64    0000000e     send_info.o (.rodata.str1.13342906380623343076.1)
                  00004d72    0000000d     send_info.o (.rodata.str1.15475178832324618403.1)
                  00004d7f    0000000d     send_info.o (.rodata.str1.6503431098811812287.1)
                  00004d8c    0000000d     send_info.o (.rodata.str1.766284786160762975.1)
                  00004d99    00000003     --HOLE-- [fill = 0]
                  00004d9c    0000000c     send_info.o (.rodata..Lswitch.table.Oscilloscope_SendStatus)
                  00004da8    0000000a     ti_msp_dl_config.o (.rodata.gUSER_UART0Config)
                  00004db2    00000002     --HOLE-- [fill = 0]
                  00004db4    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTOER_AConfig)
                  00004dbc    00000008     ti_msp_dl_config.o (.rodata.gUSER_ADC_MOTOR_VClockConfig)
                  00004dc4    00000008     ti_msp_dl_config.o (.rodata.gUSER_ADC_OSCILLOSCOPEClockConfig)
                  00004dcc    00000008     send_info.o (.rodata.str1.15772858833589009475.1)
                  00004dd4    00000007     send_info.o (.rodata.str1.12466795250469521396.1)
                  00004ddb    00000007     send_info.o (.rodata.str1.15018526337455767769.1)
                  00004de2    00000007     send_info.o (.rodata.str1.8765837587910983436.1)
                  00004de9    00000006     send_info.o (.rodata.str1.9939855383378986149.1)
                  00004def    00000005     send_info.o (.rodata.str1.3264826402484333313.1)
                  00004df4    00000004     send_info.o (.rodata.str1.1285647927986884111.1)
                  00004df8    00000008     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000619     UNINITIALIZED
                  20200000    000003e8     (.common:uart_buffer)
                  202003e8    000000bc     (.common:gTIMER_A1Backup)
                  202004a4    000000a0     (.common:gUSER_QEI_0Backup)
                  20200544    00000040     usart.o (.bss.rxProcessBuffer)
                  20200584    00000028     (.common:userKey)
                  202005ac    00000028     (.common:userKey1)
                  202005d4    00000025     (.common:tjc_parser)
                  202005f9    00000020     (.common:gRxPacket)

.data      0    20200620    00000574     UNINITIALIZED
                  20200620    000004d0     oscilloscope.o (.data.osc_config)
                  20200af0    00000028     state_machine.o (.data.sm_state)
                  20200b18    00000008     main.o (.data.allKeys)
                  20200b20    00000004     adc.o (.data.ADC_DMA_TransferComplete.dma_complete_count)
                  20200b24    00000004     adc.o (.data.ADC_StartConversion.start_count)
                  20200b28    00000004     state_machine.o (.data.StateMachine_HandleDataSending.last_serial_time)
                  20200b2c    00000004     state_machine.o (.data.StateMachine_HandleDataSending.last_status_time)
                  20200b30    00000004     state_machine.o (.data.StateMachine_HandleMotorSpeedIncrease.motor_speed)
                  20200b34    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200b38    00000004     adc.o (.data.adc_result)
                  20200b3c    00000004     main.o (.data.adc_value)
                  20200b40    00000004     usart.o (.data.bytesProcessedCount)
                  20200b44    00000004     main.o (.data.dma_ch1_interrupt_count)
                  20200b48    00000004     time.o (.data.encoderSpeed)
                  20200b4c    00000004     send_info.o (.data.encoder_position)
                  20200b50    00000004     send_info.o (.data.encoder_speed)
                  20200b54    00000004     delay.o (.data.g_tick_ms)
                  20200b58    00000004     usart.o (.data.lastDmaTransferSize)
                  20200b5c    00000004     time.o (.data.lastPosition)
                  20200b60    00000004     time.o (.data.lastTime)
                  20200b64    00000004     main.o (.data.last_adc_sample_time)
                  20200b68    00000004     main.o (.data.last_key_scan_time)
                  20200b6c    00000004     send_info.o (.data.last_position)
                  20200b70    00000004     main.o (.data.led_blink_time)
                  20200b74    00000004     motor.o (.data.motor_speed)
                  20200b78    00000004     oscilloscope.o (.data.osc_dma_interrupt_count)
                  20200b7c    00000004     motor.o (.data.period)
                  20200b80    00000004     usart.o (.data.stableCount)
                  20200b84    00000002     adc.o (.data.adc_dma_buffer)
                  20200b86    00000002     main.o (.data.prevKeyEvent)
                  20200b88    00000002     usart.o (.data.rxAvailableBytes)
                  20200b8a    00000002     usart.o (.data.rxProcessIndex)
                  20200b8c    00000001     main.o (.data.Handle_ADC_Sampling_NonBlocking.adc_state)
                  20200b8d    00000001     adc.o (.data.adc_conversion_complete)
                  20200b8e    00000001     send_info.o (.data.encoder_direction)
                  20200b8f    00000001     usart.o (.data.gRxComplete)
                  20200b90    00000001     main.o (.data.key_encoder_reset)
                  20200b91    00000001     main.o (.data.led_blink_active)
                  20200b92    00000001     motor.o (.data.motor_init)
                  20200b93    00000001     state_machine.o (.data.oscilloscope_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       main.o                         532     124       1113   
       ti_msp_dl_config.o             1072    137       348    
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1610    453       1461   
                                                               
    .\App\
       oscilloscope.o                 1820    90        1236   
       send_info.o                    1532    375       13     
       state_machine.o                752     225       53     
       tjc.o                          156     0         37     
    +--+------------------------------+-------+---------+---------+
       Total:                         4260    690       1339   
                                                               
    .\BSP\
       usart.o                        1388    63        113    
       key.o                          820     0         0      
       motor.o                        476     36        9      
       adc.o                          208     0         15     
       time.o                         144     0         12     
       delay.o                        92      0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         3128    99        153    
                                                               
    F:/Ti/ccs/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         828     0         0      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       e_sqrtf.c.obj                  160     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5798    291       4      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    F:\Ti\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatundisf.S.obj              54      0         0      
       fixunssfsi.S.obj               50      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2746    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       65        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   18374   1598      3469   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004e34 records: 2, size/record: 8, table size: 16
	.data: load addr=00004e00, load size=0000001d bytes, run addr=20200620, run size=00000574 bytes, compression=lzss
	.bss: load addr=00004e2c, load size=00000008 bytes, run addr=20200000, run size=00000619 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004e20 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001aed     00004848     00004846   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                  
-------   ----                                  
00001433  ADC0_IRQHandler                       
00001433  ADC1_IRQHandler                       
0000422d  ADC_DMA_TransferComplete              
000047ed  ADC_GetLastResult                     
00003edd  ADC_Init                              
000047f9  ADC_IsConversionComplete              
00004265  ADC_StartConversion                   
00001433  AES_IRQHandler                        
000037dc  C$$EXIT                               
00001433  CANFD0_IRQHandler                     
00001433  DAC0_IRQHandler                       
00003ff5  DL_ADC12_setClockConfig               
00004835  DL_Common_delayCycles                 
00003df9  DL_DMA_initChannel                    
00002511  DL_Timer_initFourCCPWMMode            
000028f5  DL_Timer_initTimerMode                
0000463d  DL_Timer_setCaptCompUpdateMethod      
000046ad  DL_Timer_setCaptureCompareOutCtl      
00004771  DL_Timer_setCaptureCompareValue       
00004659  DL_Timer_setClockConfig               
00003f25  DL_UART_init                          
0000474d  DL_UART_setClockConfig                
0000429d  DMA_IRQHandler                        
00001433  Default_Handler                       
00001433  GROUP0_IRQHandler                     
00001433  GROUP1_IRQHandler                     
000037dd  HOSTexit                              
00001433  HardFault_Handler                     
00001433  I2C0_IRQHandler                       
00001433  I2C1_IRQHandler                       
00002805  KET_Event_NonBlocking                 
00004035  KEY_All_Init                          
000047c1  KEY_GetEvent                          
0000114d  KEY_ScanMultiple                      
0000357d  MOTOR_Init                            
00001433  NMI_Handler                           
00004675  Oscilloscope_ClearWaveform            
00003855  Oscilloscope_DMAComplete              
00004525  Oscilloscope_ForceTrigger             
000036f5  Oscilloscope_Init                     
00000a91  Oscilloscope_MeasureWaveform          
00002095  Oscilloscope_ReadRealADC              
00003199  Oscilloscope_SendMeasurement          
00001c81  Oscilloscope_SendStatus               
00000e5d  Oscilloscope_SendWaveformData         
000045fd  Oscilloscope_SetTrigger               
00003f6d  Oscilloscope_SingleTrigger            
00003b39  Oscilloscope_Start                    
000042d5  Oscilloscope_Stop                     
00001433  PendSV_Handler                        
00004897  QEI_GetDirection                      
00004805  QEI_GetPosition                       
00003e45  QEI_GetSpeed                          
00001433  RTC_IRQHandler                        
0000489b  Reset_Handler                         
00001433  SPI0_IRQHandler                       
00001433  SPI1_IRQHandler                       
00001433  SVC_Handler                           
000046c5  SYSCFG_DL_DMA_CH0_init                
000046dd  SYSCFG_DL_DMA_CH1_init                
000046f5  SYSCFG_DL_DMA_CH2_init                
00004781  SYSCFG_DL_DMA_init                    
00003bf1  SYSCFG_DL_GPIO_init                   
000033e1  SYSCFG_DL_PWM_MOTOER_A_init           
0000441d  SYSCFG_DL_SYSCTL_init                 
0000447d  SYSCFG_DL_TIMER_A1_init               
000032c1  SYSCFG_DL_USER_ADC_MOTOR_V_init       
000038c5  SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init  
00003d51  SYSCFG_DL_USER_QEI_0_init             
00003351  SYSCFG_DL_USER_UART0_init             
00003fb1  SYSCFG_DL_init                        
00003935  SYSCFG_DL_initPower                   
00002615  Send_Debug_Info_Port                  
00004075  StateMachine_FrameReceivedCallback    
000035fd  StateMachine_HandleDataSending        
00002711  StateMachine_HandleOscilloscopeControl
00004549  StateMachine_Init                     
000022e9  StateMachine_Process                  
00004791  SysTick_Handler                       
00001433  TIMA0_IRQHandler                      
00001433  TIMA1_IRQHandler                      
00001433  TIMG0_IRQHandler                      
00001433  TIMG12_IRQHandler                     
00001433  TIMG6_IRQHandler                      
00001433  TIMG7_IRQHandler                      
00001433  TIMG8_IRQHandler                      
00004877  TJC_FrameReceivedCallback             
0000322d  TJC_ParseByte                         
00004863  TJC_ParserInit                        
00004811  UART0_IRQHandler                      
00001433  UART1_IRQHandler                      
00001433  UART2_IRQHandler                      
00001433  UART3_IRQHandler                      
00002e35  UART_Init                             
00001435  UART_ProcessReceivedData              
00002f09  UART_ProcessTJCData                   
00002ba5  UART_SendBuffer                       
000040b5  UART_SendString                       
20208000  __STACK_END                           
00000200  __STACK_SIZE                          
00000000  __TI_ATRegion0_region_sz              
00000000  __TI_ATRegion0_src_addr               
00000000  __TI_ATRegion0_trg_addr               
00000000  __TI_ATRegion1_region_sz              
00000000  __TI_ATRegion1_src_addr               
00000000  __TI_ATRegion1_trg_addr               
00000000  __TI_ATRegion2_region_sz              
00000000  __TI_ATRegion2_src_addr               
00000000  __TI_ATRegion2_trg_addr               
00004e34  __TI_CINIT_Base                       
00004e44  __TI_CINIT_Limit                      
00004e44  __TI_CINIT_Warm                       
00004e20  __TI_Handler_Table_Base               
00004e2c  __TI_Handler_Table_Limit              
000041b5  __TI_auto_init_nobinit_nopinit        
00003679  __TI_decompress_lzss                  
0000475f  __TI_decompress_none                  
00003c49  __TI_ltoa                             
ffffffff  __TI_pprof_out_hndl                   
000000c1  __TI_printfi                          
ffffffff  __TI_prof_data_size                   
ffffffff  __TI_prof_data_start                  
00000000  __TI_static_base__                    
000047b1  __TI_zero_init                        
00001af7  __adddf3                              
00002d67  __addsf3                              
000048b0  __aeabi_ctype_table_                  
000048b0  __aeabi_ctype_table_C                 
000037e1  __aeabi_d2f                           
00003e91  __aeabi_d2iz                          
00001af7  __aeabi_dadd                          
00003a75  __aeabi_dcmpeq                        
00003ab1  __aeabi_dcmpge                        
00003ac5  __aeabi_dcmpgt                        
00003a9d  __aeabi_dcmple                        
00003a89  __aeabi_dcmplt                        
00002405  __aeabi_ddiv                          
000029dd  __aeabi_dmul                          
00001aed  __aeabi_dsub                          
20200b34  __aeabi_errno                         
00004881  __aeabi_errno_addr                    
00004135  __aeabi_f2d                           
0000430d  __aeabi_f2iz                          
000043e9  __aeabi_f2uiz                         
00002d67  __aeabi_fadd                          
000034f9  __aeabi_fdiv                          
0000346d  __aeabi_fmul                          
00002d5d  __aeabi_fsub                          
000044a9  __aeabi_i2d                           
00003cf9  __aeabi_idiv                          
00001c7f  __aeabi_idiv0                         
00003cf9  __aeabi_idivmod                       
0000305b  __aeabi_ldiv0                         
0000461d  __aeabi_llsl                          
00004591  __aeabi_lmul                          
0000481d  __aeabi_memclr                        
0000481d  __aeabi_memclr4                       
0000481d  __aeabi_memclr8                       
00004889  __aeabi_memcpy                        
00004889  __aeabi_memcpy4                       
00004889  __aeabi_memcpy8                       
000047d1  __aeabi_memset                        
000047d1  __aeabi_memset4                       
000047d1  __aeabi_memset8                       
0000456d  __aeabi_ui2d                          
000044d5  __aeabi_ui2f                          
000040f5  __aeabi_uidiv                         
000040f5  __aeabi_uidivmod                      
0000437d  __aeabi_ul2f                          
00004725  __aeabi_uldivmod                      
0000461d  __ashldi3                             
ffffffff  __binit__                             
000039a5  __cmpdf2                              
00002405  __divdf3                              
000034f9  __divsf3                              
000039a5  __eqdf2                               
00004135  __extendsfdf2                         
00003e91  __fixdfsi                             
0000430d  __fixsfsi                             
000043e9  __fixunssfsi                          
000044a9  __floatsidf                           
0000437d  __floatundisf                         
0000456d  __floatunsidf                         
000044d5  __floatunsisf                         
00003769  __gedf2                               
00003769  __gtdf2                               
000039a5  __ledf2                               
000039a5  __ltdf2                               
UNDEFED   __mpu_init                            
000029dd  __muldf3                              
00004591  __muldi3                              
000041f1  __muldsi3                             
0000346d  __mulsf3                              
000039a5  __nedf2                               
20207e00  __stack                               
20200000  __start___llvm_prf_bits               
20200000  __start___llvm_prf_cnts               
20200000  __stop___llvm_prf_bits                
20200000  __stop___llvm_prf_cnts                
00001aed  __subdf3                              
00002d5d  __subsf3                              
000037e1  __truncdfsf2                          
00002fb9  __udivmoddi4                          
000044fd  _c_int00_noargs                       
UNDEFED   _system_post_cinit                    
0000489f  _system_pre_init                      
00004891  abort                                 
20200b3c  adc_value                             
20200b18  allKeys                               
00004175  atoi                                  
ffffffff  binit                                 
00004691  delay_init                            
000045b5  delay_ms                              
20200b44  dma_ch1_interrupt_count               
20200b8e  encoder_direction                     
20200b4c  encoder_position                      
20200b50  encoder_speed                         
00003b95  frexp                                 
00003b95  frexpl                                
20200b8f  gRxComplete                           
202005f9  gRxPacket                             
202003e8  gTIMER_A1Backup                       
202004a4  gUSER_QEI_0Backup                     
00004829  get_tick_ms                           
00000000  interruptVectors                      
20200b90  key_encoder_reset                     
20200b6c  last_position                         
00002c85  ldexp                                 
00002c85  ldexpl                                
00002ac1  main                                  
000045d9  memccpy                               
000030fd  memcpy                                
00003ad7  memset                                
20200b92  motor_init                            
20200b74  motor_speed                           
20200620  osc_config                            
20200b78  osc_dma_interrupt_count               
20200b7c  period                                
20200b86  prevKeyEvent                          
00002c85  scalbn                                
00002c85  scalbnl                               
00001dfd  set_motor_speed                       
20200af0  sm_state                              
00004345  sprintf                               
0000305d  sqrtf                                 
000043b5  timer_init                            
202005d4  tjc_parser                            
20200000  uart_buffer                           
20200584  userKey                               
202005ac  userKey1                              
000047a1  wcslen                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                  
-------   ----                                  
00000000  __TI_ATRegion0_region_sz              
00000000  __TI_ATRegion0_src_addr               
00000000  __TI_ATRegion0_trg_addr               
00000000  __TI_ATRegion1_region_sz              
00000000  __TI_ATRegion1_src_addr               
00000000  __TI_ATRegion1_trg_addr               
00000000  __TI_ATRegion2_region_sz              
00000000  __TI_ATRegion2_src_addr               
00000000  __TI_ATRegion2_trg_addr               
00000000  __TI_static_base__                    
00000000  interruptVectors                      
000000c1  __TI_printfi                          
00000200  __STACK_SIZE                          
00000a91  Oscilloscope_MeasureWaveform          
00000e5d  Oscilloscope_SendWaveformData         
0000114d  KEY_ScanMultiple                      
00001433  ADC0_IRQHandler                       
00001433  ADC1_IRQHandler                       
00001433  AES_IRQHandler                        
00001433  CANFD0_IRQHandler                     
00001433  DAC0_IRQHandler                       
00001433  Default_Handler                       
00001433  GROUP0_IRQHandler                     
00001433  GROUP1_IRQHandler                     
00001433  HardFault_Handler                     
00001433  I2C0_IRQHandler                       
00001433  I2C1_IRQHandler                       
00001433  NMI_Handler                           
00001433  PendSV_Handler                        
00001433  RTC_IRQHandler                        
00001433  SPI0_IRQHandler                       
00001433  SPI1_IRQHandler                       
00001433  SVC_Handler                           
00001433  TIMA0_IRQHandler                      
00001433  TIMA1_IRQHandler                      
00001433  TIMG0_IRQHandler                      
00001433  TIMG12_IRQHandler                     
00001433  TIMG6_IRQHandler                      
00001433  TIMG7_IRQHandler                      
00001433  TIMG8_IRQHandler                      
00001433  UART1_IRQHandler                      
00001433  UART2_IRQHandler                      
00001433  UART3_IRQHandler                      
00001435  UART_ProcessReceivedData              
00001aed  __aeabi_dsub                          
00001aed  __subdf3                              
00001af7  __adddf3                              
00001af7  __aeabi_dadd                          
00001c7f  __aeabi_idiv0                         
00001c81  Oscilloscope_SendStatus               
00001dfd  set_motor_speed                       
00002095  Oscilloscope_ReadRealADC              
000022e9  StateMachine_Process                  
00002405  __aeabi_ddiv                          
00002405  __divdf3                              
00002511  DL_Timer_initFourCCPWMMode            
00002615  Send_Debug_Info_Port                  
00002711  StateMachine_HandleOscilloscopeControl
00002805  KET_Event_NonBlocking                 
000028f5  DL_Timer_initTimerMode                
000029dd  __aeabi_dmul                          
000029dd  __muldf3                              
00002ac1  main                                  
00002ba5  UART_SendBuffer                       
00002c85  ldexp                                 
00002c85  ldexpl                                
00002c85  scalbn                                
00002c85  scalbnl                               
00002d5d  __aeabi_fsub                          
00002d5d  __subsf3                              
00002d67  __addsf3                              
00002d67  __aeabi_fadd                          
00002e35  UART_Init                             
00002f09  UART_ProcessTJCData                   
00002fb9  __udivmoddi4                          
0000305b  __aeabi_ldiv0                         
0000305d  sqrtf                                 
000030fd  memcpy                                
00003199  Oscilloscope_SendMeasurement          
0000322d  TJC_ParseByte                         
000032c1  SYSCFG_DL_USER_ADC_MOTOR_V_init       
00003351  SYSCFG_DL_USER_UART0_init             
000033e1  SYSCFG_DL_PWM_MOTOER_A_init           
0000346d  __aeabi_fmul                          
0000346d  __mulsf3                              
000034f9  __aeabi_fdiv                          
000034f9  __divsf3                              
0000357d  MOTOR_Init                            
000035fd  StateMachine_HandleDataSending        
00003679  __TI_decompress_lzss                  
000036f5  Oscilloscope_Init                     
00003769  __gedf2                               
00003769  __gtdf2                               
000037dc  C$$EXIT                               
000037dd  HOSTexit                              
000037e1  __aeabi_d2f                           
000037e1  __truncdfsf2                          
00003855  Oscilloscope_DMAComplete              
000038c5  SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init  
00003935  SYSCFG_DL_initPower                   
000039a5  __cmpdf2                              
000039a5  __eqdf2                               
000039a5  __ledf2                               
000039a5  __ltdf2                               
000039a5  __nedf2                               
00003a75  __aeabi_dcmpeq                        
00003a89  __aeabi_dcmplt                        
00003a9d  __aeabi_dcmple                        
00003ab1  __aeabi_dcmpge                        
00003ac5  __aeabi_dcmpgt                        
00003ad7  memset                                
00003b39  Oscilloscope_Start                    
00003b95  frexp                                 
00003b95  frexpl                                
00003bf1  SYSCFG_DL_GPIO_init                   
00003c49  __TI_ltoa                             
00003cf9  __aeabi_idiv                          
00003cf9  __aeabi_idivmod                       
00003d51  SYSCFG_DL_USER_QEI_0_init             
00003df9  DL_DMA_initChannel                    
00003e45  QEI_GetSpeed                          
00003e91  __aeabi_d2iz                          
00003e91  __fixdfsi                             
00003edd  ADC_Init                              
00003f25  DL_UART_init                          
00003f6d  Oscilloscope_SingleTrigger            
00003fb1  SYSCFG_DL_init                        
00003ff5  DL_ADC12_setClockConfig               
00004035  KEY_All_Init                          
00004075  StateMachine_FrameReceivedCallback    
000040b5  UART_SendString                       
000040f5  __aeabi_uidiv                         
000040f5  __aeabi_uidivmod                      
00004135  __aeabi_f2d                           
00004135  __extendsfdf2                         
00004175  atoi                                  
000041b5  __TI_auto_init_nobinit_nopinit        
000041f1  __muldsi3                             
0000422d  ADC_DMA_TransferComplete              
00004265  ADC_StartConversion                   
0000429d  DMA_IRQHandler                        
000042d5  Oscilloscope_Stop                     
0000430d  __aeabi_f2iz                          
0000430d  __fixsfsi                             
00004345  sprintf                               
0000437d  __aeabi_ul2f                          
0000437d  __floatundisf                         
000043b5  timer_init                            
000043e9  __aeabi_f2uiz                         
000043e9  __fixunssfsi                          
0000441d  SYSCFG_DL_SYSCTL_init                 
0000447d  SYSCFG_DL_TIMER_A1_init               
000044a9  __aeabi_i2d                           
000044a9  __floatsidf                           
000044d5  __aeabi_ui2f                          
000044d5  __floatunsisf                         
000044fd  _c_int00_noargs                       
00004525  Oscilloscope_ForceTrigger             
00004549  StateMachine_Init                     
0000456d  __aeabi_ui2d                          
0000456d  __floatunsidf                         
00004591  __aeabi_lmul                          
00004591  __muldi3                              
000045b5  delay_ms                              
000045d9  memccpy                               
000045fd  Oscilloscope_SetTrigger               
0000461d  __aeabi_llsl                          
0000461d  __ashldi3                             
0000463d  DL_Timer_setCaptCompUpdateMethod      
00004659  DL_Timer_setClockConfig               
00004675  Oscilloscope_ClearWaveform            
00004691  delay_init                            
000046ad  DL_Timer_setCaptureCompareOutCtl      
000046c5  SYSCFG_DL_DMA_CH0_init                
000046dd  SYSCFG_DL_DMA_CH1_init                
000046f5  SYSCFG_DL_DMA_CH2_init                
00004725  __aeabi_uldivmod                      
0000474d  DL_UART_setClockConfig                
0000475f  __TI_decompress_none                  
00004771  DL_Timer_setCaptureCompareValue       
00004781  SYSCFG_DL_DMA_init                    
00004791  SysTick_Handler                       
000047a1  wcslen                                
000047b1  __TI_zero_init                        
000047c1  KEY_GetEvent                          
000047d1  __aeabi_memset                        
000047d1  __aeabi_memset4                       
000047d1  __aeabi_memset8                       
000047ed  ADC_GetLastResult                     
000047f9  ADC_IsConversionComplete              
00004805  QEI_GetPosition                       
00004811  UART0_IRQHandler                      
0000481d  __aeabi_memclr                        
0000481d  __aeabi_memclr4                       
0000481d  __aeabi_memclr8                       
00004829  get_tick_ms                           
00004835  DL_Common_delayCycles                 
00004863  TJC_ParserInit                        
00004877  TJC_FrameReceivedCallback             
00004881  __aeabi_errno_addr                    
00004889  __aeabi_memcpy                        
00004889  __aeabi_memcpy4                       
00004889  __aeabi_memcpy8                       
00004891  abort                                 
00004897  QEI_GetDirection                      
0000489b  Reset_Handler                         
0000489f  _system_pre_init                      
000048b0  __aeabi_ctype_table_                  
000048b0  __aeabi_ctype_table_C                 
00004e20  __TI_Handler_Table_Base               
00004e2c  __TI_Handler_Table_Limit              
00004e34  __TI_CINIT_Base                       
00004e44  __TI_CINIT_Limit                      
00004e44  __TI_CINIT_Warm                       
20200000  __start___llvm_prf_bits               
20200000  __start___llvm_prf_cnts               
20200000  __stop___llvm_prf_bits                
20200000  __stop___llvm_prf_cnts                
20200000  uart_buffer                           
202003e8  gTIMER_A1Backup                       
202004a4  gUSER_QEI_0Backup                     
20200584  userKey                               
202005ac  userKey1                              
202005d4  tjc_parser                            
202005f9  gRxPacket                             
20200620  osc_config                            
20200af0  sm_state                              
20200b18  allKeys                               
20200b34  __aeabi_errno                         
20200b3c  adc_value                             
20200b44  dma_ch1_interrupt_count               
20200b4c  encoder_position                      
20200b50  encoder_speed                         
20200b6c  last_position                         
20200b74  motor_speed                           
20200b78  osc_dma_interrupt_count               
20200b7c  period                                
20200b86  prevKeyEvent                          
20200b8e  encoder_direction                     
20200b8f  gRxComplete                           
20200b90  key_encoder_reset                     
20200b92  motor_init                            
20207e00  __stack                               
20208000  __STACK_END                           
ffffffff  __TI_pprof_out_hndl                   
ffffffff  __TI_prof_data_size                   
ffffffff  __TI_prof_data_start                  
ffffffff  __binit__                             
ffffffff  binit                                 
UNDEFED   __mpu_init                            
UNDEFED   _system_post_cinit                    

[251 symbols]
