### 要求

## 关于程序结构

## 系统架构说明

### 核心调度机制
本项目采用集中式状态机架构，所有功能模块均通过 `App/state_machine.c` 中的状态机进行统一调度和管理。

### 命令处理流程
1. **数据接收**: 串口屏发送的命令数据通过UART+DMA方式存储在接收缓冲区中
2. **协议解析**: `tjc.c` 模块负责解析DMA缓冲区中的原始数据，按照TJC通信协议提取有效命令
3. **状态存储**: 解析后的命令信息存储在 `StateMachine_State` 类型的全局变量 `sm_state` 中
4. **命令分发**: `StateMachine_Process()` 函数根据当前页面ID进行命令大类型识别
5. **功能执行**: 在对应的页面处理函数中实现具体的命令逻辑和功能调用

### 新功能扩展流程
当需要添加新功能时，请按以下步骤进行：

1. **协议层扩展** (`tjc.h/tjc.c`)
   - 在命令类型定义中添加新的命令常量
   - 如需新页面，在页面状态定义中添加页面ID

2. **状态机层扩展** (`state_machine.h`)
   - 添加新的命令类型或页面类型定义
   - 声明新的处理函数原型

3. **逻辑层实现** (`state_machine.c`)
   - 在 `StateMachine_Process()` 中添加新页面的case分支
   - 实现对应的页面处理函数（如 `StateMachine_HandleNewPage()`）
   - 在页面处理函数中添加具体的命令响应逻辑

4. **数据发送扩展** (可选)
   - 在 `StateMachine_HandleDataSending()` 中添加新页面的数据发送逻辑
   - 实现页面特定的数据更新和显示功能

## 上位机显示的信息通关串口要这样发送

sprintf(uart_buffer, "t1.txt=\"%ld\"", (long)encoder_speed); // 发送字符串"t.txt=\"%ld\""
Send_String_With_End(uart_buffer); // 发送结束符

## 示波器界面需要你发送的数据

通道设置中
1：ch1选取状态  t_ch1.txt=
2：ch1电压/格   t_v_ch1.txt=
3：ch2选取状态  t_ch2.txt=
4：ch2电压/格   t_v_ch2.txt=
时基设置中
1：时间/格      t_time_pice.txt=
触发设置中      
1：触发模式     t_mode.txt=
2：触发电平     t_v_min.txt=
测量中
1：频率         t_forquen.txt=
2：峰峰值       t_max_max.txt=

## 上位机会发送的命令有

1：RUN/STOP 1：运行/停止
2：SINGLE   2：单次触发
3：AUTO     3：自动触发
4：FORCE    4：强制触发
5：CLEAR    5：清除波形
6：MEASURE  6：测量
7：CURSOR（暂时不需要实现） 7：光标
8：MATH（暂时不需要实现）   8：运算