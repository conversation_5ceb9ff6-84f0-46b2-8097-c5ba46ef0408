/*
 * delay.h
 * 简单延时函数头文件
 */

#ifndef DELAY_H_
#define DELAY_H_

#include "ti_msp_dl_config.h"

/**
 * @brief 初始化延时模块
 */
void delay_init(void);

/**
 * @brief 获取系统滴答计数器(毫秒)
 * @return 系统运行的毫秒数
 */
uint32_t get_tick_ms(void);

/**
 * @brief 秒级延时
 * @param s 延时秒数
 */
void delay_s(uint32_t s);

/**
 * @brief 毫秒级延时
 * @param ms 延时毫秒数
 */
void delay_ms(uint32_t ms);

/**
 * @brief 微秒级延时
 * @param us 延时微秒数
 */
void delay_us(uint32_t us);

#endif
