/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_MOTOER_A */
#define PWM_MOTOER_A_INST                                                  TIMG0
#define PWM_MOTOER_A_INST_IRQHandler                            TIMG0_IRQHandler
#define PWM_MOTOER_A_INST_INT_IRQN                              (TIMG0_INT_IRQn)
#define PWM_MOTOER_A_INST_CLK_FREQ                                      32000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_MOTOER_A_C0_PORT                                          GPIOA
#define GPIO_PWM_MOTOER_A_C0_PIN                                  DL_GPIO_PIN_12
#define GPIO_PWM_MOTOER_A_C0_IOMUX                               (IOMUX_PINCM34)
#define GPIO_PWM_MOTOER_A_C0_IOMUX_FUNC              IOMUX_PINCM34_PF_TIMG0_CCP0
#define GPIO_PWM_MOTOER_A_C0_IDX                             DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_MOTOER_A_C1_PORT                                          GPIOA
#define GPIO_PWM_MOTOER_A_C1_PIN                                  DL_GPIO_PIN_13
#define GPIO_PWM_MOTOER_A_C1_IOMUX                               (IOMUX_PINCM35)
#define GPIO_PWM_MOTOER_A_C1_IOMUX_FUNC              IOMUX_PINCM35_PF_TIMG0_CCP1
#define GPIO_PWM_MOTOER_A_C1_IDX                             DL_TIMER_CC_1_INDEX




/* Defines for USER_QEI_0 */
#define USER_QEI_0_INST                                                    TIMG8
#define USER_QEI_0_INST_IRQHandler                              TIMG8_IRQHandler
#define USER_QEI_0_INST_INT_IRQN                                (TIMG8_INT_IRQn)
/* Pin configuration defines for USER_QEI_0 PHA Pin */
#define GPIO_USER_QEI_0_PHA_PORT                                           GPIOB
#define GPIO_USER_QEI_0_PHA_PIN                                    DL_GPIO_PIN_6
#define GPIO_USER_QEI_0_PHA_IOMUX                                (IOMUX_PINCM23)
#define GPIO_USER_QEI_0_PHA_IOMUX_FUNC               IOMUX_PINCM23_PF_TIMG8_CCP0
/* Pin configuration defines for USER_QEI_0 PHB Pin */
#define GPIO_USER_QEI_0_PHB_PORT                                           GPIOB
#define GPIO_USER_QEI_0_PHB_PIN                                   DL_GPIO_PIN_16
#define GPIO_USER_QEI_0_PHB_IOMUX                                (IOMUX_PINCM33)
#define GPIO_USER_QEI_0_PHB_IOMUX_FUNC               IOMUX_PINCM33_PF_TIMG8_CCP1


/* Defines for TIMER_A1 */
#define TIMER_A1_INST                                                    (TIMA0)
#define TIMER_A1_INST_IRQHandler                                TIMA0_IRQHandler
#define TIMER_A1_INST_INT_IRQN                                  (TIMA0_INT_IRQn)
#define TIMER_A1_INST_LOAD_VALUE                                         (1023U)



/* Defines for USER_UART0 */
#define USER_UART0_INST                                                    UART0
#define USER_UART0_INST_FREQUENCY                                       32000000
#define USER_UART0_INST_IRQHandler                              UART0_IRQHandler
#define USER_UART0_INST_INT_IRQN                                  UART0_INT_IRQn
#define GPIO_USER_UART0_RX_PORT                                            GPIOA
#define GPIO_USER_UART0_TX_PORT                                            GPIOA
#define GPIO_USER_UART0_RX_PIN                                    DL_GPIO_PIN_11
#define GPIO_USER_UART0_TX_PIN                                    DL_GPIO_PIN_10
#define GPIO_USER_UART0_IOMUX_RX                                 (IOMUX_PINCM22)
#define GPIO_USER_UART0_IOMUX_TX                                 (IOMUX_PINCM21)
#define GPIO_USER_UART0_IOMUX_RX_FUNC                  IOMUX_PINCM22_PF_UART0_RX
#define GPIO_USER_UART0_IOMUX_TX_FUNC                  IOMUX_PINCM21_PF_UART0_TX
#define USER_UART0_BAUD_RATE                                            (921600)
#define USER_UART0_IBRD_32_MHZ_921600_BAUD                                   (2)
#define USER_UART0_FBRD_32_MHZ_921600_BAUD                                  (11)





/* Defines for USER_ADC_MOTOR_V */
#define USER_ADC_MOTOR_V_INST                                               ADC0
#define USER_ADC_MOTOR_V_INST_IRQHandler                         ADC0_IRQHandler
#define USER_ADC_MOTOR_V_INST_INT_IRQN                           (ADC0_INT_IRQn)
#define USER_ADC_MOTOR_V_ADCMEM_0                             DL_ADC12_MEM_IDX_0
#define USER_ADC_MOTOR_V_ADCMEM_0_REF            DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define USER_ADC_MOTOR_V_ADCMEM_0_REF_VOLTAGE_V                                     3.3
#define GPIO_USER_ADC_MOTOR_V_C2_PORT                                      GPIOA
#define GPIO_USER_ADC_MOTOR_V_C2_PIN                              DL_GPIO_PIN_25

/* Defines for USER_ADC_OSCILLOSCOPE */
#define USER_ADC_OSCILLOSCOPE_INST                                          ADC1
#define USER_ADC_OSCILLOSCOPE_INST_IRQHandler                         ADC1_IRQHandler
#define USER_ADC_OSCILLOSCOPE_INST_INT_IRQN                         (ADC1_INT_IRQn)
#define USER_ADC_OSCILLOSCOPE_ADCMEM_0                        DL_ADC12_MEM_IDX_0
#define USER_ADC_OSCILLOSCOPE_ADCMEM_0_REF         DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define USER_ADC_OSCILLOSCOPE_ADCMEM_0_REF_VOLTAGE_V                                     3.3
#define GPIO_USER_ADC_OSCILLOSCOPE_C1_PORT                                   GPIOA
#define GPIO_USER_ADC_OSCILLOSCOPE_C1_PIN                          DL_GPIO_PIN_16
#define GPIO_USER_ADC_OSCILLOSCOPE_IOMUX_C1                      (IOMUX_PINCM38)
#define GPIO_USER_ADC_OSCILLOSCOPE_IOMUX_C1_FUNC  (IOMUX_PINCM38_PF_UNCONNECTED)



/* Defines for DMA_CH1 */
#define DMA_CH1_CHAN_ID                                                      (1)
#define USER_ADC_MOTOR_V_INST_DMA_TRIGGER             (DMA_ADC0_EVT_GEN_BD_TRIG)
/* Defines for DMA_CH2 */
#define DMA_CH2_CHAN_ID                                                      (2)
#define USER_ADC_OSCILLOSCOPE_INST_DMA_TRIGGER         (DMA_ADC1_EVT_GEN_BD_TRIG)
/* Defines for DMA_CH0 */
#define DMA_CH0_CHAN_ID                                                      (0)
#define USER_UART0_INST_DMA_TRIGGER                          (DMA_UART0_RX_TRIG)


/* Port definition for Pin Group USER_LED_1 */
#define USER_LED_1_PORT                                                  (GPIOA)

/* Defines for PIN_A_0: GPIOA.0 with pinCMx 1 on package pin 33 */
#define USER_LED_1_PIN_A_0_PIN                                   (DL_GPIO_PIN_0)
#define USER_LED_1_PIN_A_0_IOMUX                                  (IOMUX_PINCM1)
/* Port definition for Pin Group USER_SWITCH_2 */
#define USER_SWITCH_2_PORT                                               (GPIOB)

/* Defines for PIN_B_21: GPIOB.21 with pinCMx 49 on package pin 20 */
#define USER_SWITCH_2_PIN_B_21_PIN                              (DL_GPIO_PIN_21)
#define USER_SWITCH_2_PIN_B_21_IOMUX                             (IOMUX_PINCM49)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_MOTOER_A_init(void);
void SYSCFG_DL_USER_QEI_0_init(void);
void SYSCFG_DL_TIMER_A1_init(void);
void SYSCFG_DL_USER_UART0_init(void);
void SYSCFG_DL_USER_ADC_MOTOR_V_init(void);
void SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init(void);
void SYSCFG_DL_DMA_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
