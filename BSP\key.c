/*
 * key.c
 * 按键模块化处理实现文件
 */

#include "key.h"

// 按键初始化参数定义
// 可以根据实际需要调整参数
static const struct {
    GPIO_Regs *port;
    uint32_t pin;
    bool activeHigh;
    uint32_t longPressTime;
    uint32_t debounceTime;
    uint8_t id;
} KEY_Config[] = {
    {USER_SWITCH_2_PORT, USER_SWITCH_2_PIN_B_21_PIN, false, 1000, 50, KEY_ID_USER},
    // 可以在此添加更多按键配置
    // 例如: {GPIOB, DL_GPIO_PIN_0, false, 2000, 50, KEY_ID_MENU},
};

// 按键数量
#define KEY_COUNT (sizeof(KEY_Config) / sizeof(KEY_Config[0]))

/**
 * @brief 初始化按键
 * 
 * @param key 按键结构体指针
 * @param port 按键所在的GPIO端口
 * @param pin 按键对应的引脚
 * @param activeHigh 按键激活电平，true=高电平有效，false=低电平有效
 * @param longPressTime 长按时间阈值（毫秒）
 * @param debounceTime 去抖时间（毫秒）
 * @param id 按键ID，用于区分不同按键
 */
void KEY_Init(Key_t *key, GPIO_Regs *port, uint32_t pin, 
              bool activeHigh, uint32_t longPressTime, uint32_t debounceTime, uint8_t id)
{
    // 初始化按键结构体参数
    key->port = port;
    key->pin = pin;
    key->activeHigh = activeHigh;
    key->longPressTime = longPressTime;
    key->debounceTime = debounceTime;
    key->state = KEY_RELEASED;
    key->pressTime = 0;
    key->debounceCounter = 0;
    key->stablePressed = false;
    key->id = id;
    
    // 读取当前按键电平作为初始状态
    uint32_t pinStatus = DL_GPIO_readPins(key->port, key->pin);
    key->prevLevel = (pinStatus & key->pin) ? true : false;
}

/**
 * @brief 初始化所有按键
 * 
 * @param keys 按键结构体数组指针
 */
void KEY_All_Init(Key_t *keys[])
{
    // 初始化所有按键
    for (uint8_t i = 0; i < KEY_COUNT; i++) {
        if (keys[i] != NULL) {
            KEY_Init(keys[i], 
                     KEY_Config[i].port, 
                     KEY_Config[i].pin,
                     KEY_Config[i].activeHigh,
                     KEY_Config[i].longPressTime,
                     KEY_Config[i].debounceTime,
                     KEY_Config[i].id);
        }
    }
}

/**
 * @brief 扫描按键状态
 * 
 * @param key 按键结构体指针
 * @param timeIncMs 时间增量（毫秒），用于计算长按时间
 */
void KEY_Scan(Key_t *key, uint32_t timeIncMs)
{
    // 读取当前按键电平
    uint32_t pinStatus = DL_GPIO_readPins(key->port, key->pin);
    bool currentLevel = (pinStatus & key->pin) ? true : false;
    
    // 根据激活电平确定按键是否被按下
    bool isPressed = (key->activeHigh) ? currentLevel : !currentLevel;
    
    // 去抖处理
    if (isPressed != key->stablePressed) {
        // 电平变化，增加去抖计数器
        key->debounceCounter += timeIncMs;
        
        // 如果计数器超过去抖时间，认为按键状态稳定
        if (key->debounceCounter >= key->debounceTime) {
            key->stablePressed = isPressed;
            key->debounceCounter = 0;
        }
    } else {
        // 电平稳定，重置去抖计数器
        key->debounceCounter = 0;
    }
    
    // 按键状态检测与更新（使用去抖后的稳定状态）
    if (key->stablePressed) {
        // 按键按下状态
        if (key->state == KEY_RELEASED) {
            // 之前是释放状态，现在变为按下状态
            key->state = KEY_PRESSED;
            key->pressTime = 0;
        } else {
            // 按键持续按下，增加按压时间计数
            key->pressTime += timeIncMs;
            
            // 检查是否达到长按阈值
            if (key->pressTime >= key->longPressTime && key->state != KEY_LONG_PRESSED) {
                key->state = KEY_LONG_PRESSED;
            }
        }
    } else {
        // 按键释放状态
        if (key->state != KEY_RELEASED) {
            // 之前是按下状态，现在变为释放状态
            key->state = KEY_RELEASED;
            key->pressTime = 0;
        }
    }
    
    // 更新上一次按键电平
    key->prevLevel = currentLevel;
}

/**
 * @brief 获取按键状态
 * 
 * @param key 按键结构体指针
 * @return KeyState_t 按键状态
 */
KeyState_t KEY_GetState(Key_t *key)
{
    return key->state;
}

/**
 * @brief 获取按键事件
 * 
 * @param key 按键结构体指针
 * @return uint16_t 按键事件码（按键ID和状态的组合）
 */
uint16_t KEY_GetEvent(Key_t *key)
{
    return KEY_MAKE_EVENT(key->id, key->state);
}

/**
 * @brief 同时扫描多个按键状态
 * 
 * @param keys 按键结构体指针数组
 * @param numKeys 按键数量
 * @param timeIncMs 时间增量（毫秒）
 */
void KEY_ScanMultiple(Key_t *keys[], uint8_t numKeys, uint32_t timeIncMs)
{
    // 遍历所有按键并分别扫描状态
    for (uint8_t i = 0; i < numKeys; i++) {
        if (keys[i] != NULL) {
            KEY_Scan(keys[i], timeIncMs);
        }
    }
}

/**
 * @brief 检查指定按键是否被按下（包括短按和长按）
 * 
 * @param key 按键结构体指针
 * @return true 按键被按下
 * @return false 按键未被按下
 */
bool KEY_IsPressed(Key_t *key)
{
    return (key->state == KEY_PRESSED || key->state == KEY_LONG_PRESSED);
}

/**
 * @brief 检查指定按键是否被长按
 * 
 * @param key 按键结构体指针
 * @return true 按键被长按
 * @return false 按键未被长按
 */
bool KEY_IsLongPressed(Key_t *key)
{
    return (key->state == KEY_LONG_PRESSED);
} 