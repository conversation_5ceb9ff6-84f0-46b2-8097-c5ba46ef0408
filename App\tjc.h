/**
 * @file tjc.h
 * @brief TJC串口屏通信协议处理
 */

#ifndef TJC_H_
#define TJC_H_

#include "usart.h"
#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

/* 协议常量定义 */
#define TJC_FRAME_START          0x61    // 帧起始标识 ('a')
#define TJC_FRAME_END            0xFF    // 帧结束标识
#define TJC_MAX_DATA_SIZE        32      // 最大数据长度

extern char uart_buffer[1000]; // UART发送缓冲区

// 0xAA     XX      XX      XX      XX     0xFF
// 帧头 当前界面id 命令类型 数据长度 数据内容  帧尾
/* 页面状态定义 */
#define PAGE_MAIN 0          // 主界面 - 只负责界面跳转
#define PAGE_MOTOR 1         // 电机控制界面 - 处理电机相关命令
#define PAGE_OSCILLOSCOPE 2  // 示波器界面 - 处理示波器相关命令

/* 命令类型定义 */
#define CMD_NAVIGATION 0x01     // 界面跳转命令
#define CMD_MOTOR_CONTROL 0x02  // 电机控制命令
#define CMD_OSCILLOSCOPE 0x03   // 示波器命令

/* 界面跳转命令定义 */
typedef enum {
    NAV_TO_MAIN = 0x00,         // 跳转到主界面
    NAV_TO_MOTOR = 0x01,        // 跳转到电机控制界面
    NAV_TO_OSCILLOSCOPE = 0x02  // 跳转到示波器界面
} NavigationCommand;

/* 电机控制命令定义 */
typedef enum {
    MOTOR_CMD_INCREASE_SPEED = 0x01, // 增加速度
    MOTOR_CMD_DECREASE_SPEED = 0x02, // 减少速度
    MOTOR_CMD_START = 0x03,          // 启动电机
    MOTOR_CMD_STOP = 0x04,           // 停止电机
    MOTOR_CMD_REVERSE = 0x05         // 反向运动
} MotorCommand;

/* 示波器命令定义 */
typedef enum {
    OSC_CMD_START = 0x01,       // RUN - 开始采样
    OSC_CMD_STOP = 0x02,        // STOP - 停止采样
    OSC_CMD_SET_SCALE = 0x03,   // 设置量程
    OSC_CMD_TRIGGER = 0x04,     // 触发设置
    OSC_CMD_SINGLE = 0x05,      // SINGLE - 单次触发
    OSC_CMD_AUTO = 0x06,        // AUTO - 自动触发
    OSC_CMD_FORCE = 0x07,       // FORCE - 强制触发
    OSC_CMD_CLEAR = 0x08,       // CLEAR - 清除波形
    OSC_CMD_MEASURE = 0x09,     // MEASURE - 测量
    OSC_CMD_SET_INTERVAL = 0x0A, // 设置发送间隔
    OSC_CMD_AUTO_SEND = 0x0B,   // 启用/禁用自动发送
    OSC_CMD_DEBUG = 0x0C,       // 调试状态检查
    OSC_CMD_TEST_DATA = 0x0D,   // 强制填充测试数据
    OSC_CMD_REAL_ADC = 0x0E,    // 读取真实ADC数据
    OSC_CMD_SEND_WAVEFORM = 0x0F // 发送波形数据到上位机
} OscilloscopeCommand;

/* 协议帧结构 */
typedef struct {
    uint8_t page_id;                    // 当前界面
    uint8_t command;                    // 命令类型
    uint8_t length;                     // 数据长度
    uint8_t data[TJC_MAX_DATA_SIZE];    // 数据内容
} TJC_Frame;

/* 解析状态机状态 */
typedef enum {
    TJC_STATE_WAIT_START,              // 等待起始字节
    TJC_STATE_WAIT_PAGE_ID,            // 等待页面ID字节
    TJC_STATE_WAIT_COMMAND,            // 等待命令字节
    TJC_STATE_WAIT_LENGTH,             // 等待长度字节
    TJC_STATE_WAIT_DATA,               // 等待数据字节
    TJC_STATE_WAIT_END                 // 等待结束字节
} TJC_ParseState;

/* 解析器结构 */
typedef struct {
    TJC_ParseState state;              // 当前解析状态
    TJC_Frame frame;                   // 当前解析中的帧
    uint8_t dataIndex;                 // 当前数据索引
} TJC_Parser;

/* 回调函数类型定义 */
typedef void (*TJC_FrameCallback)(const TJC_Frame* frame);

/**
 * @brief 初始化TJC协议解析器
 * @param parser 解析器指针
 */
void TJC_ParserInit(TJC_Parser* parser);

/**
 * @brief 处理接收到的字节
 * @param parser 解析器指针
 * @param byte 接收到的字节
 * @param callback 帧接收完成回调函数
 * @return 是否成功解析到一个完整帧
 */
bool TJC_ParseByte(TJC_Parser* parser, uint8_t byte, TJC_FrameCallback callback);

// 声明TJC解析器实例（在tjc.c中定义）
extern TJC_Parser tjc_parser;

#endif /* __TJC_H__ */
