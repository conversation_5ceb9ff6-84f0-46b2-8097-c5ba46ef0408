/*
 * delay.c
 * 简单延时函数实现文件
 */

#include "delay.h"

/* 系统时钟频率 */
#define SYSTEM_CLOCK_FREQ 32000000  // 32MHz

/* 全局变量 - 系统滴答计数器(毫秒) */
static volatile uint32_t g_tick_ms = 0;

/**
 * @brief SysTick中断处理函数
 */
void SysTick_Handler(void) {
    g_tick_ms++;
}

/**
 * @brief 初始化延时模块
 */
void delay_init(void) {
    /* 配置SysTick定时器 - 1ms周期 */
    SysTick->CTRL = 0;                         // 禁用SysTick
    SysTick->LOAD = (SYSTEM_CLOCK_FREQ / 1000) - 1; // 设置1ms周期
    SysTick->VAL = 0;                          // 清零当前值
    SysTick->CTRL = 7;                         // 启用SysTick，使用内核时钟，启用中断
}

/**
 * @brief 获取系统滴答计数器(毫秒)
 * @return 系统运行的毫秒数
 */
uint32_t get_tick_ms(void) {
    return g_tick_ms;
}

/**
 * @brief 秒级延时
 * @param s 延时秒数
 */
void delay_s(uint32_t s) {
    uint32_t start = get_tick_ms();
    while (get_tick_ms() - start < s * 1000);
}

/**
 * @brief 毫秒级延时
 * @param ms 延时毫秒数
 */
void delay_ms(uint32_t ms) {
    uint32_t start = get_tick_ms();
    while (get_tick_ms() - start < ms);
}

/**
 * @brief 微秒级延时
 * @param us 延时微秒数
 */
void delay_us(uint32_t us) {
    /* 使用简单的循环延时，精度较低 */
    uint32_t cycles = (SYSTEM_CLOCK_FREQ / 1000000) * us / 4; // 预估每条指令4个周期
    while (cycles--);
}