/**
 * @file dsp_spectrum.c
 * @brief DSP频谱分析模块实现
 * @version 1.0
 * @date 2025-01-29
 */

#include "dsp_spectrum.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>

/* 全局频谱分析句柄 */
DSP_Spectrum_Handle g_spectrum_handle = {0};

/* 预定义的频谱分析配置 */
const DSP_Spectrum_Config DSP_SPECTRUM_CONFIG_DEFAULT = {
    .spectrum_length = DSP_FFT_SIZE_512 / 2,
    .sample_rate = DSP_SAMPLE_RATE_16K,
    .frequency_resolution = (DSP_Float)DSP_SAMPLE_RATE_16K / DSP_FFT_SIZE_512,
    .max_frequency = DSP_SAMPLE_RATE_16K / 2,
    .enable_peak_detection = true,
    .enable_frequency_measurement = true,
    .enable_power_calculation = true,
    .peak_threshold_percent = 10,
    .max_peaks = 10
};

const DSP_Spectrum_Config DSP_SPECTRUM_CONFIG_AUDIO = {
    .spectrum_length = DSP_FFT_SIZE_1024 / 2,
    .sample_rate = DSP_SAMPLE_RATE_48K,
    .frequency_resolution = (DSP_Float)DSP_SAMPLE_RATE_48K / DSP_FFT_SIZE_1024,
    .max_frequency = DSP_SAMPLE_RATE_48K / 2,
    .enable_peak_detection = true,
    .enable_frequency_measurement = true,
    .enable_power_calculation = true,
    .peak_threshold_percent = 5,
    .max_peaks = 20
};

const DSP_Spectrum_Config DSP_SPECTRUM_CONFIG_SIGNAL_ANALYSIS = {
    .spectrum_length = DSP_FFT_SIZE_512 / 2,
    .sample_rate = DSP_SAMPLE_RATE_16K,
    .frequency_resolution = (DSP_Float)DSP_SAMPLE_RATE_16K / DSP_FFT_SIZE_512,
    .max_frequency = DSP_SAMPLE_RATE_16K / 2,
    .enable_peak_detection = true,
    .enable_frequency_measurement = true,
    .enable_power_calculation = true,
    .peak_threshold_percent = 15,
    .max_peaks = 5
};

/* 私有函数声明 */
static DSP_Status DSP_Spectrum_AllocateBuffers(DSP_Spectrum_Handle *handle);
static DSP_Status DSP_Spectrum_FreeBuffers(DSP_Spectrum_Handle *handle);
static DSP_Status DSP_Spectrum_ValidateHandle(DSP_Spectrum_Handle *handle);
static DSP_Status DSP_Spectrum_ValidateConfig(const DSP_Spectrum_Config *config);
static bool DSP_Spectrum_IsPeak(const DSP_Float *data, uint16_t index, uint16_t length, DSP_Float threshold);

/**
 * @brief 初始化频谱分析模块
 */
DSP_Status DSP_Spectrum_Init(DSP_Spectrum_Handle *handle, 
                             DSP_FFT_Handle *fft_handle,
                             const DSP_Spectrum_Config *config)
{
    DSP_ASSERT(handle != NULL);
    DSP_ASSERT(fft_handle != NULL);
    DSP_ASSERT(config != NULL);
    
    DSP_DEBUG("Initializing spectrum analysis module");
    
    /* 验证配置参数 */
    DSP_Status status = DSP_Spectrum_ValidateConfig(config);
    if (status != DSP_STATUS_OK) {
        DSP_ERROR("Invalid spectrum configuration");
        return status;
    }
    
    /* 清零句柄 */
    memset(handle, 0, sizeof(DSP_Spectrum_Handle));
    
    /* 设置基本参数 */
    handle->fft_handle = fft_handle;
    handle->config = *config;
    handle->initialized = false;
    
    /* 分配缓冲区 */
    status = DSP_Spectrum_AllocateBuffers(handle);
    if (status != DSP_STATUS_OK) {
        DSP_ERROR("Failed to allocate spectrum buffers");
        return status;
    }
    
    handle->initialized = true;
    DSP_DEBUG("Spectrum analysis module initialized successfully");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 反初始化频谱分析模块
 */
DSP_Status DSP_Spectrum_DeInit(DSP_Spectrum_Handle *handle)
{
    DSP_Status status = DSP_Spectrum_ValidateHandle(handle);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    DSP_DEBUG("De-initializing spectrum analysis module");
    
    /* 释放缓冲区 */
    DSP_Spectrum_FreeBuffers(handle);
    
    /* 清零句柄 */
    memset(handle, 0, sizeof(DSP_Spectrum_Handle));
    
    DSP_DEBUG("Spectrum analysis module de-initialized");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 执行频谱分析
 */
DSP_Status DSP_Spectrum_Analyze(DSP_Spectrum_Handle *handle, 
                                const DSP_Float *input_data,
                                DSP_Spectrum_Result *result)
{
    DSP_ASSERT(handle != NULL);
    DSP_ASSERT(input_data != NULL);
    DSP_ASSERT(result != NULL);
    
    DSP_Status status = DSP_Spectrum_ValidateHandle(handle);
    if (status != DSP_STATUS_OK) {
        return status;
    }
    
    DSP_PERF_START();
    
    /* 执行FFT变换 */
    DSP_FFT_Result fft_result;
    status = DSP_FFT_Compute(handle->fft_handle, input_data, &fft_result);
    if (status != DSP_STATUS_OK) {
        DSP_ERROR("FFT computation failed");
        return status;
    }
    
    /* 复制幅度谱 */
    memcpy(handle->result.magnitude_spectrum, fft_result.magnitude, 
           handle->config.spectrum_length * sizeof(DSP_Float));
    
    /* 计算功率谱 */
    if (handle->config.enable_power_calculation) {
        status = DSP_Spectrum_ComputePowerSpectrum(handle, 
                                                  handle->result.magnitude_spectrum,
                                                  handle->result.power_spectrum);
        if (status != DSP_STATUS_OK) {
            DSP_ERROR("Power spectrum computation failed");
            return status;
        }
        
        /* 计算总功率 */
        status = DSP_Spectrum_ComputeTotalPower(handle->result.power_spectrum,
                                               handle->config.spectrum_length,
                                               &handle->result.total_power);
        if (status != DSP_STATUS_OK) {
            DSP_ERROR("Total power computation failed");
            return status;
        }
    }
    
    /* 计算dB幅度谱 */
    status = DSP_Spectrum_ComputeMagnitudeDb(handle->result.magnitude_spectrum,
                                            handle->result.magnitude_db,
                                            handle->config.spectrum_length);
    if (status != DSP_STATUS_OK) {
        DSP_ERROR("dB magnitude computation failed");
        return status;
    }
    
    /* 峰值检测 */
    if (handle->config.enable_peak_detection) {
        status = DSP_Spectrum_DetectPeaks(handle,
                                         handle->result.magnitude_spectrum,
                                         handle->result.peaks,
                                         handle->config.max_peaks,
                                         &handle->result.peak_count);
        if (status != DSP_STATUS_OK) {
            DSP_ERROR("Peak detection failed");
            return status;
        }
        
        /* 查找主频和基频 */
        if (handle->result.peak_count > 0) {
            DSP_Spectrum_FindDominantFrequency(handle->result.peaks,
                                              handle->result.peak_count,
                                              &handle->result.dominant_frequency);
            
            DSP_Spectrum_FindFundamentalFrequency(handle->result.peaks,
                                                 handle->result.peak_count,
                                                 &handle->result.fundamental_frequency);
        }
    }
    
    /* 计算频率重心 */
    if (handle->config.enable_frequency_measurement) {
        status = DSP_Spectrum_ComputeCentroid(handle->result.magnitude_spectrum,
                                             handle->config.spectrum_length,
                                             handle->config.sample_rate,
                                             &handle->result.frequency_centroid);
        if (status != DSP_STATUS_OK) {
            DSP_ERROR("Centroid computation failed");
            return status;
        }
        
        /* 计算频谱滚降点 */
        status = DSP_Spectrum_ComputeRolloff(handle->result.magnitude_spectrum,
                                            handle->config.spectrum_length,
                                            handle->config.sample_rate,
                                            85.0f,  // 85%滚降点
                                            &handle->result.spectral_rolloff);
        if (status != DSP_STATUS_OK) {
            DSP_ERROR("Rolloff computation failed");
            return status;
        }
    }
    
    /* 设置结果 */
    handle->result.valid = true;
    *result = handle->result;
    
    DSP_PERF_END("Spectrum_Analyze");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 计算功率谱密度
 */
DSP_Status DSP_Spectrum_ComputePowerSpectrum(DSP_Spectrum_Handle *handle,
                                             const DSP_Float *magnitude_spectrum,
                                             DSP_Float *power_spectrum)
{
    DSP_ASSERT(handle != NULL);
    DSP_ASSERT(magnitude_spectrum != NULL);
    DSP_ASSERT(power_spectrum != NULL);
    
    /* 功率谱 = 幅度谱的平方 */
    for (uint16_t i = 0; i < handle->config.spectrum_length; i++) {
        power_spectrum[i] = magnitude_spectrum[i] * magnitude_spectrum[i];
    }
    
    DSP_DEBUG("Power spectrum computed");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 计算dB幅度谱
 */
DSP_Status DSP_Spectrum_ComputeMagnitudeDb(const DSP_Float *magnitude_spectrum,
                                           DSP_Float *magnitude_db,
                                           uint16_t length)
{
    DSP_ASSERT(magnitude_spectrum != NULL);
    DSP_ASSERT(magnitude_db != NULL);
    DSP_ASSERT(length > 0);
    
    for (uint16_t i = 0; i < length; i++) {
        magnitude_db[i] = DSP_FFT_MagnitudeToDb(magnitude_spectrum[i]);
    }
    
    DSP_DEBUG("dB magnitude spectrum computed");
    
    return DSP_STATUS_OK;
}

/**
 * @brief 检测频谱峰值
 */
DSP_Status DSP_Spectrum_DetectPeaks(DSP_Spectrum_Handle *handle,
                                    const DSP_Float *magnitude_spectrum,
                                    DSP_Peak_Info *peaks,
                                    uint16_t max_peaks,
                                    uint16_t *found_peaks)
{
    DSP_ASSERT(handle != NULL);
    DSP_ASSERT(magnitude_spectrum != NULL);
    DSP_ASSERT(peaks != NULL);
    DSP_ASSERT(found_peaks != NULL);
    
    /* 计算阈值 */
    uint32_t max_index;
    DSP_Float max_magnitude;
    arm_max_f32(magnitude_spectrum, handle->config.spectrum_length, &max_magnitude, &max_index);
    
    DSP_Float threshold = max_magnitude * handle->config.peak_threshold_percent / 100.0f;
    
    uint16_t peak_count = 0;
    
    /* 搜索峰值 */
    for (uint16_t i = 1; i < handle->config.spectrum_length - 1 && peak_count < max_peaks; i++) {
        if (DSP_Spectrum_IsPeak(magnitude_spectrum, i, handle->config.spectrum_length, threshold)) {
            peaks[peak_count].index = i;
            peaks[peak_count].frequency = DSP_FFT_IndexToFrequency(i, 
                                                                  handle->config.sample_rate,
                                                                  handle->config.spectrum_length * 2);
            peaks[peak_count].magnitude = magnitude_spectrum[i];
            peaks[peak_count].magnitude_db = DSP_FFT_MagnitudeToDb(magnitude_spectrum[i]);
            peaks[peak_count].power = magnitude_spectrum[i] * magnitude_spectrum[i];
            
            peak_count++;
        }
    }
    
    *found_peaks = peak_count;
    
    DSP_DEBUG("Detected %u peaks with threshold %.3f", peak_count, threshold);

    return DSP_STATUS_OK;
}

/**
 * @brief 查找主频
 */
DSP_Status DSP_Spectrum_FindDominantFrequency(const DSP_Peak_Info *peaks,
                                              uint16_t peak_count,
                                              DSP_Float *dominant_frequency)
{
    DSP_ASSERT(peaks != NULL);
    DSP_ASSERT(dominant_frequency != NULL);

    if (peak_count == 0) {
        *dominant_frequency = 0.0f;
        return DSP_STATUS_OK;
    }

    /* 查找幅度最大的峰值 */
    uint16_t max_peak_index = 0;
    DSP_Float max_magnitude = peaks[0].magnitude;

    for (uint16_t i = 1; i < peak_count; i++) {
        if (peaks[i].magnitude > max_magnitude) {
            max_magnitude = peaks[i].magnitude;
            max_peak_index = i;
        }
    }

    *dominant_frequency = peaks[max_peak_index].frequency;

    DSP_DEBUG("Dominant frequency: %.2f Hz", *dominant_frequency);

    return DSP_STATUS_OK;
}

/**
 * @brief 查找基频
 */
DSP_Status DSP_Spectrum_FindFundamentalFrequency(const DSP_Peak_Info *peaks,
                                                 uint16_t peak_count,
                                                 DSP_Float *fundamental_frequency)
{
    DSP_ASSERT(peaks != NULL);
    DSP_ASSERT(fundamental_frequency != NULL);

    if (peak_count == 0) {
        *fundamental_frequency = 0.0f;
        return DSP_STATUS_OK;
    }

    /* 简单实现：选择频率最低的显著峰值作为基频 */
    DSP_Float min_frequency = peaks[0].frequency;

    for (uint16_t i = 1; i < peak_count; i++) {
        if (peaks[i].frequency < min_frequency && peaks[i].frequency > 50.0f) {
            min_frequency = peaks[i].frequency;
        }
    }

    *fundamental_frequency = min_frequency;

    DSP_DEBUG("Fundamental frequency: %.2f Hz", *fundamental_frequency);

    return DSP_STATUS_OK;
}

/**
 * @brief 计算频率重心
 */
DSP_Status DSP_Spectrum_ComputeCentroid(const DSP_Float *magnitude_spectrum,
                                        uint16_t length,
                                        uint32_t sample_rate,
                                        DSP_Float *centroid)
{
    DSP_ASSERT(magnitude_spectrum != NULL);
    DSP_ASSERT(centroid != NULL);
    DSP_ASSERT(length > 0);

    DSP_Float weighted_sum = 0.0f;
    DSP_Float magnitude_sum = 0.0f;
    DSP_Float frequency_resolution = (DSP_Float)sample_rate / (length * 2);

    for (uint16_t i = 0; i < length; i++) {
        DSP_Float frequency = i * frequency_resolution;
        weighted_sum += frequency * magnitude_spectrum[i];
        magnitude_sum += magnitude_spectrum[i];
    }

    if (magnitude_sum > 0.0f) {
        *centroid = weighted_sum / magnitude_sum;
    } else {
        *centroid = 0.0f;
    }

    DSP_DEBUG("Frequency centroid: %.2f Hz", *centroid);

    return DSP_STATUS_OK;
}

/**
 * @brief 计算频谱滚降点
 */
DSP_Status DSP_Spectrum_ComputeRolloff(const DSP_Float *magnitude_spectrum,
                                       uint16_t length,
                                       uint32_t sample_rate,
                                       DSP_Float rolloff_percent,
                                       DSP_Float *rolloff_frequency)
{
    DSP_ASSERT(magnitude_spectrum != NULL);
    DSP_ASSERT(rolloff_frequency != NULL);
    DSP_ASSERT(length > 0);
    DSP_ASSERT(rolloff_percent > 0.0f && rolloff_percent <= 100.0f);

    /* 计算总能量 */
    DSP_Float total_energy = 0.0f;
    for (uint16_t i = 0; i < length; i++) {
        total_energy += magnitude_spectrum[i] * magnitude_spectrum[i];
    }

    if (total_energy == 0.0f) {
        *rolloff_frequency = 0.0f;
        return DSP_STATUS_OK;
    }

    /* 查找滚降点 */
    DSP_Float target_energy = total_energy * rolloff_percent / 100.0f;
    DSP_Float cumulative_energy = 0.0f;
    DSP_Float frequency_resolution = (DSP_Float)sample_rate / (length * 2);

    for (uint16_t i = 0; i < length; i++) {
        cumulative_energy += magnitude_spectrum[i] * magnitude_spectrum[i];
        if (cumulative_energy >= target_energy) {
            *rolloff_frequency = i * frequency_resolution;
            break;
        }
    }

    DSP_DEBUG("Spectral rolloff (%.0f%%): %.2f Hz", rolloff_percent, *rolloff_frequency);

    return DSP_STATUS_OK;
}

/**
 * @brief 计算总功率
 */
DSP_Status DSP_Spectrum_ComputeTotalPower(const DSP_Float *power_spectrum,
                                          uint16_t length,
                                          DSP_Float *total_power)
{
    DSP_ASSERT(power_spectrum != NULL);
    DSP_ASSERT(total_power != NULL);
    DSP_ASSERT(length > 0);

    DSP_Float sum = 0.0f;
    for (uint16_t i = 0; i < length; i++) {
        sum += power_spectrum[i];
    }

    *total_power = sum;

    DSP_DEBUG("Total power: %.3f", *total_power);

    return DSP_STATUS_OK;
}

/**
 * @brief 频谱平滑处理
 */
DSP_Status DSP_Spectrum_Smooth(DSP_Float *spectrum,
                               uint16_t length,
                               DSP_Float smooth_factor)
{
    DSP_ASSERT(spectrum != NULL);
    DSP_ASSERT(length > 0);
    DSP_ASSERT(smooth_factor >= 0.0f && smooth_factor <= 1.0f);

    if (smooth_factor == 0.0f) {
        return DSP_STATUS_OK;  // 无平滑
    }

    /* 简单的移动平均平滑 */
    DSP_Float *temp_buffer = (DSP_Float*)DSP_MALLOC(length * sizeof(DSP_Float));
    if (temp_buffer == NULL) {
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    memcpy(temp_buffer, spectrum, length * sizeof(DSP_Float));

    uint16_t window_size = (uint16_t)(smooth_factor * 10) + 1;  // 1-11的窗口大小
    uint16_t half_window = window_size / 2;

    for (uint16_t i = half_window; i < length - half_window; i++) {
        DSP_Float sum = 0.0f;
        for (uint16_t j = i - half_window; j <= i + half_window; j++) {
            sum += temp_buffer[j];
        }
        spectrum[i] = sum / window_size;
    }

    DSP_FREE(temp_buffer);

    DSP_DEBUG("Spectrum smoothed with factor %.2f", smooth_factor);

    return DSP_STATUS_OK;
}

/* 私有函数实现 */

/**
 * @brief 分配频谱分析缓冲区
 */
static DSP_Status DSP_Spectrum_AllocateBuffers(DSP_Spectrum_Handle *handle)
{
    if (handle == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    DSP_DEBUG("Allocating spectrum buffers: length=%u", handle->config.spectrum_length);

    /* 分配幅度谱缓冲区 */
    handle->result.magnitude_spectrum = (DSP_Float*)DSP_MALLOC(handle->config.spectrum_length * sizeof(DSP_Float));
    if (handle->result.magnitude_spectrum == NULL) {
        DSP_ERROR("Failed to allocate magnitude spectrum buffer");
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    /* 分配功率谱缓冲区 */
    if (handle->config.enable_power_calculation) {
        handle->result.power_spectrum = (DSP_Float*)DSP_MALLOC(handle->config.spectrum_length * sizeof(DSP_Float));
        if (handle->result.power_spectrum == NULL) {
            DSP_ERROR("Failed to allocate power spectrum buffer");
            DSP_Spectrum_FreeBuffers(handle);
            return DSP_STATUS_INSUFFICIENT_MEMORY;
        }
    }

    /* 分配dB幅度谱缓冲区 */
    handle->result.magnitude_db = (DSP_Float*)DSP_MALLOC(handle->config.spectrum_length * sizeof(DSP_Float));
    if (handle->result.magnitude_db == NULL) {
        DSP_ERROR("Failed to allocate dB magnitude buffer");
        DSP_Spectrum_FreeBuffers(handle);
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    /* 分配峰值信息缓冲区 */
    if (handle->config.enable_peak_detection) {
        handle->result.peaks = (DSP_Peak_Info*)DSP_MALLOC(handle->config.max_peaks * sizeof(DSP_Peak_Info));
        if (handle->result.peaks == NULL) {
            DSP_ERROR("Failed to allocate peaks buffer");
            DSP_Spectrum_FreeBuffers(handle);
            return DSP_STATUS_INSUFFICIENT_MEMORY;
        }
    }

    /* 分配临时缓冲区 */
    handle->temp_buffer = (DSP_Float*)DSP_MALLOC(handle->config.spectrum_length * sizeof(DSP_Float));
    if (handle->temp_buffer == NULL) {
        DSP_ERROR("Failed to allocate temp buffer");
        DSP_Spectrum_FreeBuffers(handle);
        return DSP_STATUS_INSUFFICIENT_MEMORY;
    }

    DSP_DEBUG("All spectrum buffers allocated successfully");

    return DSP_STATUS_OK;
}

/**
 * @brief 释放频谱分析缓冲区
 */
static DSP_Status DSP_Spectrum_FreeBuffers(DSP_Spectrum_Handle *handle)
{
    if (handle == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    DSP_DEBUG("Freeing spectrum buffers");

    if (handle->result.magnitude_spectrum) {
        DSP_FREE(handle->result.magnitude_spectrum);
        handle->result.magnitude_spectrum = NULL;
    }

    if (handle->result.power_spectrum) {
        DSP_FREE(handle->result.power_spectrum);
        handle->result.power_spectrum = NULL;
    }

    if (handle->result.magnitude_db) {
        DSP_FREE(handle->result.magnitude_db);
        handle->result.magnitude_db = NULL;
    }

    if (handle->result.peaks) {
        DSP_FREE(handle->result.peaks);
        handle->result.peaks = NULL;
    }

    if (handle->temp_buffer) {
        DSP_FREE(handle->temp_buffer);
        handle->temp_buffer = NULL;
    }

    DSP_DEBUG("All spectrum buffers freed");

    return DSP_STATUS_OK;
}

/**
 * @brief 验证频谱分析句柄有效性
 */
static DSP_Status DSP_Spectrum_ValidateHandle(DSP_Spectrum_Handle *handle)
{
    if (handle == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    if (!handle->initialized) {
        return DSP_STATUS_NOT_INITIALIZED;
    }

    if (handle->fft_handle == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    return DSP_STATUS_OK;
}

/**
 * @brief 验证频谱分析配置有效性
 */
static DSP_Status DSP_Spectrum_ValidateConfig(const DSP_Spectrum_Config *config)
{
    if (config == NULL) {
        return DSP_STATUS_INVALID_PARAM;
    }

    if (config->spectrum_length == 0 || config->spectrum_length > DSP_FFT_MAX_SIZE / 2) {
        DSP_ERROR("Invalid spectrum length: %u", config->spectrum_length);
        return DSP_STATUS_INVALID_PARAM;
    }

    if (config->sample_rate == 0) {
        DSP_ERROR("Invalid sample rate: %lu", config->sample_rate);
        return DSP_STATUS_INVALID_PARAM;
    }

    if (config->peak_threshold_percent > 100) {
        DSP_ERROR("Invalid peak threshold: %u%%", config->peak_threshold_percent);
        return DSP_STATUS_INVALID_PARAM;
    }

    if (config->max_peaks == 0) {
        DSP_ERROR("Invalid max peaks: %u", config->max_peaks);
        return DSP_STATUS_INVALID_PARAM;
    }

    return DSP_STATUS_OK;
}

/**
 * @brief 判断是否为峰值
 */
static bool DSP_Spectrum_IsPeak(const DSP_Float *data, uint16_t index, uint16_t length, DSP_Float threshold)
{
    if (index == 0 || index >= length - 1) {
        return false;
    }

    /* 检查幅度是否超过阈值 */
    if (data[index] < threshold) {
        return false;
    }

    /* 检查是否为局部最大值 */
    if (data[index] > data[index - 1] && data[index] > data[index + 1]) {
        return true;
    }

    return false;
}
