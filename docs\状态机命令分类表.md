# 状态机命令分类表

## 协议帧格式
```
┌──────┬──────────┬──────────┬──────────┬──────────┬──────┐
│ 0x61 │ Page ID  │ Command  │ Length   │   Data   │ 0xFF │
│(帧头) │(页面ID)  │(命令类型)│(数据长度)│(数据内容)│(帧尾)│
└──────┴──────────┴──────────┴──────────┴──────────┴──────┘
```

## 页面状态定义

| 页面ID | 十六进制 | 页面名称 | 功能描述 |
|--------|----------|----------|----------|
| `PAGE_MAIN` | `0x00` | 主界面 | 负责界面跳转控制 |
| `PAGE_MOTOR` | `0x01` | 电机控制界面 | 处理电机相关命令 |
| `PAGE_OSCILLOSCOPE` | `0x02` | 示波器界面 | 处理示波器相关命令 |

## 命令类型分类

### 1. 界面跳转命令 (CMD_NAVIGATION = 0x01)

| 命令名称 | 十六进制值 | 功能描述 | 适用页面 | 数据长度 |
|----------|------------|----------|----------|----------|
| `NAV_TO_MAIN` | `0x00` | 跳转到主界面 | 任意页面 | 1字节 |
| `NAV_TO_MOTOR` | `0x01` | 跳转到电机控制界面 | 任意页面 | 1字节 |
| `NAV_TO_OSCILLOSCOPE` | `0x02` | 跳转到示波器界面 | 任意页面 | 1字节 |

**完整命令格式示例**：
- 跳转到主界面：`61 XX 01 01 00 FF`
- 跳转到电机界面：`61 XX 01 01 01 FF`
- 跳转到示波器界面：`61 XX 01 01 02 FF`

### 2. 电机控制命令 (CMD_MOTOR_CONTROL = 0x02)

| 命令名称 | 十六进制值 | 功能描述 | 适用页面 | 数据长度 | 实现函数 |
|----------|------------|----------|----------|----------|----------|
| `MOTOR_CMD_INCREASE_SPEED` | `0x01` | 增加电机速度(+10%) | PAGE_MOTOR | 1字节 | `StateMachine_HandleMotorControl()` |
| `MOTOR_CMD_DECREASE_SPEED` | `0x02` | 减少电机速度(-10%) | PAGE_MOTOR | 1字节 | `StateMachine_HandleMotorControl()` |
| `MOTOR_CMD_START` | `0x03` | 启动电机(默认50%速度) | PAGE_MOTOR | 1字节 | `StateMachine_HandleMotorControl()` |
| `MOTOR_CMD_STOP` | `0x04` | 停止电机 | PAGE_MOTOR | 1字节 | `StateMachine_HandleMotorControl()` |
| `MOTOR_CMD_REVERSE` | `0x05` | 电机反向运动 | PAGE_MOTOR | 1字节 | `StateMachine_HandleMotorControl()` |

**完整命令格式示例**：
- 启动电机：`61 01 02 01 03 FF`
- 停止电机：`61 01 02 01 04 FF`
- 增加速度：`61 01 02 01 01 FF`
- 减少速度：`61 01 02 01 02 FF`
- 反向运动：`61 01 02 01 05 FF`

### 3. 示波器控制命令 (CMD_OSCILLOSCOPE = 0x03)

| 命令名称 | 十六进制值 | 功能描述 | 适用页面 | 数据长度 | 实现函数 |
|----------|------------|----------|----------|----------|----------|
| `OSC_CMD_RUN_STOP` | `0x01` | 运行/停止切换 | PAGE_OSCILLOSCOPE | 1字节 | `StateMachine_HandleOscilloscopeControl()` |
| `OSC_CMD_SINGLE` | `0x02` | 单次触发 | PAGE_OSCILLOSCOPE | 1字节 | `StateMachine_HandleOscilloscopeControl()` |
| `OSC_CMD_AUTO` | `0x03` | 自动触发模式 | PAGE_OSCILLOSCOPE | 1字节 | `StateMachine_HandleOscilloscopeControl()` |
| `OSC_CMD_FORCE` | `0x04` | 强制触发 | PAGE_OSCILLOSCOPE | 1字节 | `StateMachine_HandleOscilloscopeControl()` |
| `OSC_CMD_CLEAR` | `0x05` | 清除波形数据 | PAGE_OSCILLOSCOPE | 1字节 | `StateMachine_HandleOscilloscopeControl()` |
| `OSC_CMD_MEASURE` | `0x06` | 执行测量 | PAGE_OSCILLOSCOPE | 1字节 | `StateMachine_HandleOscilloscopeControl()` |

**完整命令格式示例**：
- 运行/停止：`61 02 03 01 01 FF`
- 单次触发：`61 02 03 01 02 FF`
- 自动触发：`61 02 03 01 03 FF`
- 强制触发：`61 02 03 01 04 FF`
- 清除波形：`61 02 03 01 05 FF`
- 执行测量：`61 02 03 01 06 FF`

## 按键事件处理

除了串口命令外，系统还支持物理按键事件：

| 事件类型 | 触发条件 | 适用页面 | 功能描述 |
|----------|----------|----------|----------|
| 短按事件 | 按键按下并释放 | PAGE_MOTOR | 电机加速(+10%) |
| 长按事件 | 按键长时间按下 | PAGE_MOTOR | 电机停止 |
| 释放事件 | 按键释放 | 任意页面 | 停止LED闪烁等 |

## 命令处理流程

### 1. 命令解析流程
```
UART接收 → TJC协议解析 → 状态机分发 → 页面处理函数 → 具体功能执行
```

### 2. 状态机分发逻辑
```c
switch (sm_state.current_page_id) {
    case PAGE_MAIN:
        StateMachine_HandleMainPage();      // 处理界面跳转
        break;
    case PAGE_MOTOR:
        StateMachine_HandleMotorPage();     // 处理电机控制
        break;
    case PAGE_OSCILLOSCOPE:
        StateMachine_HandleOscilloscopePage(); // 处理示波器控制
        break;
}
```

### 3. 命令有效性检查
- 命令必须在对应的页面才能执行
- 界面跳转命令可在任意页面执行
- 电机命令只在电机页面有效
- 示波器命令只在示波器页面有效

## 扩展命令添加指南

### 1. 添加新的命令类型
1. 在`tjc.h`中定义新的`CMD_XXX`常量
2. 在对应的枚举中添加具体命令
3. 在`state_machine.c`中添加处理函数
4. 更新本文档

### 2. 添加新的页面
1. 在`tjc.h`中定义新的`PAGE_XXX`常量
2. 在状态机中添加新的case分支
3. 实现对应的页面处理函数
4. 更新导航命令枚举

## 调试信息

系统会通过UART发送调试信息，格式如下：
- 电机命令执行反馈
- 示波器命令执行反馈
- 未知命令错误提示
- 页面切换确认信息

所有命令都经过严格的参数检查和错误处理，确保系统的稳定性和可靠性。
