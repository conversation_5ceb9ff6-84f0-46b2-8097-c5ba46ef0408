/**
 * @file dsp_fft.h
 * @brief DSP FFT模块头文件
 * @version 1.0
 * @date 2025-01-29
 * 
 * 本文件定义了FFT变换的接口和数据结构
 * 基于CMSIS-DSP库实现高效的FFT运算
 */

#ifndef __DSP_FFT_H__
#define __DSP_FFT_H__

#include "dsp_config.h"
#include "dsp_core.h"

/* CMSIS-DSP库包含 */
#define ARM_MATH_CM0PLUS
#include "arm_math.h"

/* FFT实例结构定义 */
typedef struct {
    arm_rfft_fast_instance_f32 rfft_instance;  // 实数FFT实例
    arm_cfft_instance_f32 cfft_instance;       // 复数FFT实例
    uint16_t fft_size;                         // FFT点数
    bool initialized;                          // 初始化标志
    DSP_WindowType window_type;                // 窗函数类型
    DSP_Float *window_buffer;                  // 窗函数缓冲区
    DSP_Float *input_buffer;                   // 输入缓冲区
    DSP_Float *output_buffer;                  // 输出缓冲区（复数）
    DSP_Float *magnitude_buffer;               // 幅度谱缓冲区
    DSP_Float *phase_buffer;                   // 相位谱缓冲区
} DSP_FFT_Handle;

/* FFT配置结构 */
typedef struct {
    uint16_t fft_size;                         // FFT点数
    uint32_t sample_rate;                      // 采样率
    DSP_WindowType window_type;                // 窗函数类型
    bool use_window;                           // 是否使用窗函数
    bool compute_magnitude;                    // 是否计算幅度谱
    bool compute_phase;                        // 是否计算相位谱
} DSP_FFT_Config;

/* FFT结果结构 */
typedef struct {
    DSP_Float *magnitude;                      // 幅度谱指针
    DSP_Float *phase;                          // 相位谱指针
    DSP_Float *complex_output;                 // 复数输出指针
    uint16_t spectrum_length;                  // 频谱长度
    DSP_Float frequency_resolution;            // 频率分辨率
    DSP_Float max_frequency;                   // 最大频率
    bool valid;                                // 结果有效标志
} DSP_FFT_Result;

/* FFT初始化和管理函数 */

/**
 * @brief 初始化FFT模块
 * @param handle FFT句柄指针
 * @param config FFT配置指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_Init(DSP_FFT_Handle *handle, const DSP_FFT_Config *config);

/**
 * @brief 反初始化FFT模块
 * @param handle FFT句柄指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_DeInit(DSP_FFT_Handle *handle);

/**
 * @brief 重置FFT模块
 * @param handle FFT句柄指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_Reset(DSP_FFT_Handle *handle);

/* FFT计算函数 */

/**
 * @brief 执行实数FFT变换
 * @param handle FFT句柄指针
 * @param input 输入数据指针
 * @param result 结果结构指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_Compute(DSP_FFT_Handle *handle, const DSP_Float *input, DSP_FFT_Result *result);

/**
 * @brief 执行复数FFT变换
 * @param handle FFT句柄指针
 * @param input_real 实部输入指针
 * @param input_imag 虚部输入指针
 * @param result 结果结构指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_ComputeComplex(DSP_FFT_Handle *handle, 
                                  const DSP_Float *input_real, 
                                  const DSP_Float *input_imag, 
                                  DSP_FFT_Result *result);

/**
 * @brief 执行逆FFT变换
 * @param handle FFT句柄指针
 * @param input 复数输入指针
 * @param output 输出数据指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_ComputeInverse(DSP_FFT_Handle *handle, 
                                  const DSP_Float *input, 
                                  DSP_Float *output);

/* 频谱分析函数 */

/**
 * @brief 计算功率谱密度
 * @param handle FFT句柄指针
 * @param complex_data 复数数据指针
 * @param psd_output 功率谱密度输出指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_ComputePSD(DSP_FFT_Handle *handle, 
                              const DSP_Float *complex_data, 
                              DSP_Float *psd_output);

/**
 * @brief 查找频谱峰值
 * @param magnitude 幅度谱指针
 * @param length 数据长度
 * @param peak_index 峰值索引指针
 * @param peak_value 峰值大小指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_FindPeak(const DSP_Float *magnitude, 
                            uint16_t length, 
                            uint16_t *peak_index, 
                            DSP_Float *peak_value);

/**
 * @brief 查找多个频谱峰值
 * @param magnitude 幅度谱指针
 * @param length 数据长度
 * @param peak_indices 峰值索引数组指针
 * @param peak_values 峰值大小数组指针
 * @param max_peaks 最大峰值数量
 * @param found_peaks 找到的峰值数量指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_FindMultiplePeaks(const DSP_Float *magnitude, 
                                     uint16_t length,
                                     uint16_t *peak_indices, 
                                     DSP_Float *peak_values,
                                     uint16_t max_peaks, 
                                     uint16_t *found_peaks);

/* 窗函数相关函数 */

/**
 * @brief 应用窗函数
 * @param handle FFT句柄指针
 * @param input 输入数据指针
 * @param output 输出数据指针
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_ApplyWindow(DSP_FFT_Handle *handle, 
                               const DSP_Float *input, 
                               DSP_Float *output);

/**
 * @brief 生成窗函数系数
 * @param window_type 窗函数类型
 * @param coeffs 系数缓冲区指针
 * @param length 窗函数长度
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_GenerateWindow(DSP_WindowType window_type, 
                                  DSP_Float *coeffs, 
                                  uint16_t length);

/* 工具函数 */

/**
 * @brief 获取FFT输出的频率分辨率
 * @param handle FFT句柄指针
 * @param sample_rate 采样率
 * @return DSP_Float 频率分辨率
 */
DSP_Float DSP_FFT_GetFrequencyResolution(DSP_FFT_Handle *handle, uint32_t sample_rate);

/**
 * @brief 将FFT索引转换为频率
 * @param index FFT索引
 * @param sample_rate 采样率
 * @param fft_size FFT点数
 * @return DSP_Float 对应频率
 */
DSP_Float DSP_FFT_IndexToFrequency(uint16_t index, uint32_t sample_rate, uint16_t fft_size);

/**
 * @brief 将频率转换为FFT索引
 * @param frequency 频率
 * @param sample_rate 采样率
 * @param fft_size FFT点数
 * @return uint16_t 对应索引
 */
uint16_t DSP_FFT_FrequencyToIndex(DSP_Float frequency, uint32_t sample_rate, uint16_t fft_size);

/**
 * @brief 计算dB值
 * @param magnitude 幅度值
 * @return DSP_Float dB值
 */
DSP_Float DSP_FFT_MagnitudeToDb(DSP_Float magnitude);

/**
 * @brief 归一化幅度谱
 * @param magnitude 幅度谱指针
 * @param length 数据长度
 * @param max_value 最大值指针（可选）
 * @return DSP_Status 操作状态
 */
DSP_Status DSP_FFT_NormalizeMagnitude(DSP_Float *magnitude, 
                                      uint16_t length, 
                                      DSP_Float *max_value);

/* 全局FFT句柄声明 */
extern DSP_FFT_Handle g_fft_handle;

/* 预定义的FFT配置 */
extern const DSP_FFT_Config DSP_FFT_CONFIG_DEFAULT;
extern const DSP_FFT_Config DSP_FFT_CONFIG_AUDIO_16K;
extern const DSP_FFT_Config DSP_FFT_CONFIG_AUDIO_48K;

#endif /* __DSP_FFT_H__ */
