@echo off
"F:/Ti/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt" -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"F:/Ti/work/key/App" -I"F:/Ti/work/key/BSP" -I"F:/Ti/work/key" -I"F:/Ti/work/key/Debug" -I"F:/Ti/ccs/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"F:/Ti/ccs/mspm0_sdk_2_05_00_05/source" -gdwarf-3 -MMD -MP -MF"App/oscilloscope.d_raw" -MT"App/oscilloscope.o" -o"App/oscilloscope.o" "../App/oscilloscope.c"
echo Compilation complete
