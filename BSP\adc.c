/*
 * ADC.c
 * ADC转换模块化处理文件
 */

#include "adc.h"

// 内部变量
static volatile bool adc_conversion_complete = false;
static volatile int adc_result = 0;

// DMA缓冲区 - 用于存储ADC DMA传输的数据
static volatile uint16_t adc_dma_buffer[1] = {0};

/**
 * @brief 初始化ADC模块
 */
void ADC_Init(void) {
    // 配置DMA传输
    adc_conversion_complete = false;
    adc_result = 0;

    // 暂时跳过手动引脚配置，先测试DMA是否工作

    // 配置DMA目标地址为我们的缓冲区
    DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID,
                      (uint32_t)DL_ADC12_getMemResultAddress(USER_ADC_MOTOR_V_INST,
                      USER_ADC_MOTOR_V_ADCMEM_0));
    DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)adc_dma_buffer);
    DL_DMA_setTransferSize(DMA, DMA_CH1_CHAN_ID, 1);

    // 启用DMA中断而不是ADC中断
    DL_DMA_enableInterrupt(DMA, DL_DMA_INTERRUPT_CHANNEL1);
    NVIC_EnableIRQ(DMA_INT_IRQn);

}

/**
 * @brief 获取指定通道的ADC值
 * @param channel ADC通道
 * @return int 转换结果
 */
int ADC_GetValue(ADC_Channel_t channel) {
    // 重置转换完成标志
    adc_conversion_complete = false;
    
    // 可以在此添加通道选择代码
    // DL_ADC12_selectSingleSampleChannel(USER_ADC_MOTOR_V_INST, (uint32_t)channel);
    
    // 启动ADC转换
    DL_ADC12_startConversion(USER_ADC_MOTOR_V_INST);
    
    // 等待转换完成
    while (adc_conversion_complete == false ) {
    }
    
    // 轮询方式获取结果
    adc_result = DL_ADC12_getMemResult(USER_ADC_MOTOR_V_INST, DL_ADC12_MEM_IDX_0);
    DL_ADC12_enableConversions(USER_ADC_MOTOR_V_INST);
    return adc_result;
}

/**
 * @brief 启动ADC转换（非阻塞）
 * @param channel ADC通道
 */
void ADC_StartConversion(ADC_Channel_t channel) {
    // 重置转换完成标志
    adc_conversion_complete = false;

    // 启用DMA通道
    DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);

    // 启动ADC转换，DMA会自动传输数据
    DL_ADC12_startConversion(USER_ADC_MOTOR_V_INST);

    // 调试计数
    static uint32_t start_count = 0;
    start_count++;

}

/**
 * @brief 检查ADC转换是否完成
 * @return true 转换完成，false 转换进行中
 */
bool ADC_IsConversionComplete(void) {
    return adc_conversion_complete;
}

/**
 * @brief 获取最后一次ADC转换结果（非阻塞）
 * @return int 转换结果
 */
int ADC_GetLastResult(void) {
    // 从DMA缓冲区读取结果
    return (int)adc_dma_buffer[0];
}

/**
 * @brief ADC DMA传输完成处理函数
 * 这个函数将被main.c中的DMA_IRQHandler调用
 */
void ADC_DMA_TransferComplete(void) {
    // DMA通道1传输完成（ADC数据）
    adc_result = (int)adc_dma_buffer[0];
    adc_conversion_complete = true;

    // 禁用DMA通道，等待下次启动
    DL_DMA_disableChannel(DMA, DMA_CH1_CHAN_ID);

    // 调试信息：记录DMA传输完成次数和数据
    static uint32_t dma_complete_count = 0;
    dma_complete_count++;

    // 这里不能直接调用UART发送，因为在中断中
    // 只记录数据，在主循环中发送

}