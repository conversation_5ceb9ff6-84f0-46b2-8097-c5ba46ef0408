/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.c =============
 *  Configured MSPM0 DriverLib module definitions
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */

#include "ti_msp_dl_config.h"

DL_TimerG_backupConfig gUSER_QEI_0Backup;
DL_TimerA_backupConfig gTIMER_A1Backup;

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform any initialization needed before using any board APIs
 */
SYSCONFIG_WEAK void SYSCFG_DL_init(void)
{
    SYSCFG_DL_initPower();
    SYSCFG_DL_GPIO_init();
    /* Module-Specific Initializations*/
    SYSCFG_DL_SYSCTL_init();
    SYSCFG_DL_PWM_MOTOER_A_init();
    SYSCFG_DL_USER_QEI_0_init();
    SYSCFG_DL_TIMER_A1_init();
    SYSCFG_DL_USER_UART0_init();
    SYSCFG_DL_USER_ADC_MOTOR_V_init();
    SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init();
    SYSCFG_DL_DMA_init();
    /* Ensure backup structures have no valid state */

	gUSER_QEI_0Backup.backupRdy 	= false;
	gTIMER_A1Backup.backupRdy 	= false;


}
/*
 * User should take care to save and restore register configuration in application.
 * See Retention Configuration section for more details.
 */
SYSCONFIG_WEAK bool SYSCFG_DL_saveConfiguration(void)
{
    bool retStatus = true;

	retStatus &= DL_TimerG_saveConfiguration(USER_QEI_0_INST, &gUSER_QEI_0Backup);
	retStatus &= DL_TimerA_saveConfiguration(TIMER_A1_INST, &gTIMER_A1Backup);

    return retStatus;
}


SYSCONFIG_WEAK bool SYSCFG_DL_restoreConfiguration(void)
{
    bool retStatus = true;

	retStatus &= DL_TimerG_restoreConfiguration(USER_QEI_0_INST, &gUSER_QEI_0Backup, false);
	retStatus &= DL_TimerA_restoreConfiguration(TIMER_A1_INST, &gTIMER_A1Backup, false);

    return retStatus;
}

SYSCONFIG_WEAK void SYSCFG_DL_initPower(void)
{
    DL_GPIO_reset(GPIOA);
    DL_GPIO_reset(GPIOB);
    DL_TimerG_reset(PWM_MOTOER_A_INST);
    DL_TimerG_reset(USER_QEI_0_INST);
    DL_TimerA_reset(TIMER_A1_INST);
    DL_UART_Main_reset(USER_UART0_INST);
    DL_ADC12_reset(USER_ADC_MOTOR_V_INST);
    DL_ADC12_reset(USER_ADC_OSCILLOSCOPE_INST);


    DL_GPIO_enablePower(GPIOA);
    DL_GPIO_enablePower(GPIOB);
    DL_TimerG_enablePower(PWM_MOTOER_A_INST);
    DL_TimerG_enablePower(USER_QEI_0_INST);
    DL_TimerA_enablePower(TIMER_A1_INST);
    DL_UART_Main_enablePower(USER_UART0_INST);
    DL_ADC12_enablePower(USER_ADC_MOTOR_V_INST);
    DL_ADC12_enablePower(USER_ADC_OSCILLOSCOPE_INST);

    delay_cycles(POWER_STARTUP_DELAY);
}

SYSCONFIG_WEAK void SYSCFG_DL_GPIO_init(void)
{

    DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_MOTOER_A_C0_IOMUX,GPIO_PWM_MOTOER_A_C0_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_PWM_MOTOER_A_C0_PORT, GPIO_PWM_MOTOER_A_C0_PIN);
    DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_MOTOER_A_C1_IOMUX,GPIO_PWM_MOTOER_A_C1_IOMUX_FUNC);
    DL_GPIO_enableOutput(GPIO_PWM_MOTOER_A_C1_PORT, GPIO_PWM_MOTOER_A_C1_PIN);

    DL_GPIO_initPeripheralInputFunction(GPIO_USER_QEI_0_PHA_IOMUX,GPIO_USER_QEI_0_PHA_IOMUX_FUNC);
    DL_GPIO_initPeripheralInputFunction(GPIO_USER_QEI_0_PHB_IOMUX,GPIO_USER_QEI_0_PHB_IOMUX_FUNC);

    DL_GPIO_initPeripheralOutputFunction(
        GPIO_USER_UART0_IOMUX_TX, GPIO_USER_UART0_IOMUX_TX_FUNC);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_USER_UART0_IOMUX_RX, GPIO_USER_UART0_IOMUX_RX_FUNC);

    
	DL_GPIO_setAnalogInternalResistor(GPIO_USER_ADC_OSCILLOSCOPE_IOMUX_C1, DL_GPIO_RESISTOR_NONE);

    DL_GPIO_initDigitalOutputFeatures(USER_LED_1_PIN_A_0_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_DRIVE_STRENGTH_LOW, DL_GPIO_HIZ_DISABLE);

    DL_GPIO_initDigitalInputFeatures(USER_SWITCH_2_PIN_B_21_IOMUX,
		 DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
		 DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    DL_GPIO_clearPins(USER_LED_1_PORT, USER_LED_1_PIN_A_0_PIN);
    DL_GPIO_enableOutput(USER_LED_1_PORT, USER_LED_1_PIN_A_0_PIN);

}



SYSCONFIG_WEAK void SYSCFG_DL_SYSCTL_init(void)
{

	//Low Power Mode is configured to be SLEEP0
    DL_SYSCTL_setBORThreshold(DL_SYSCTL_BOR_THRESHOLD_LEVEL_0);

    
	DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
	/* Set default configuration */
	DL_SYSCTL_disableHFXT();
	DL_SYSCTL_disableSYSPLL();

}


/*
 * Timer clock configuration to be sourced by  / 1 (32000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   32000000 Hz = 32000000 Hz / (1 * (0 + 1))
 */
static const DL_TimerG_ClockConfig gPWM_MOTOER_AClockConfig = {
    .clockSel = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_1,
    .prescale = 0U
};

static const DL_TimerG_PWMConfig gPWM_MOTOER_AConfig = {
    .pwmMode = DL_TIMER_PWM_MODE_EDGE_ALIGN_UP,
    .period = 32000,
    .isTimerWithFourCC = false,
    .startTimer = DL_TIMER_STOP,
};

SYSCONFIG_WEAK void SYSCFG_DL_PWM_MOTOER_A_init(void) {

    DL_TimerG_setClockConfig(
        PWM_MOTOER_A_INST, (DL_TimerG_ClockConfig *) &gPWM_MOTOER_AClockConfig);

    DL_TimerG_initPWMMode(
        PWM_MOTOER_A_INST, (DL_TimerG_PWMConfig *) &gPWM_MOTOER_AConfig);

    // Set Counter control to the smallest CC index being used
    DL_TimerG_setCounterControl(PWM_MOTOER_A_INST,DL_TIMER_CZC_CCCTL0_ZCOND,DL_TIMER_CAC_CCCTL0_ACOND,DL_TIMER_CLC_CCCTL0_LCOND);

    DL_TimerG_setCaptureCompareOutCtl(PWM_MOTOER_A_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERG_CAPTURE_COMPARE_0_INDEX);

    DL_TimerG_setCaptCompUpdateMethod(PWM_MOTOER_A_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERG_CAPTURE_COMPARE_0_INDEX);
    DL_TimerG_setCaptureCompareValue(PWM_MOTOER_A_INST, 0, DL_TIMER_CC_0_INDEX);

    DL_TimerG_setCaptureCompareOutCtl(PWM_MOTOER_A_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
		DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
		DL_TIMERG_CAPTURE_COMPARE_1_INDEX);

    DL_TimerG_setCaptCompUpdateMethod(PWM_MOTOER_A_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, DL_TIMERG_CAPTURE_COMPARE_1_INDEX);
    DL_TimerG_setCaptureCompareValue(PWM_MOTOER_A_INST, 0, DL_TIMER_CC_1_INDEX);

    DL_TimerG_enableClock(PWM_MOTOER_A_INST);


    
    DL_TimerG_setCCPDirection(PWM_MOTOER_A_INST , DL_TIMER_CC0_OUTPUT | DL_TIMER_CC1_OUTPUT );


}


static const DL_TimerG_ClockConfig gUSER_QEI_0ClockConfig = {
    .clockSel = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_8,
    .prescale = 199U
};


SYSCONFIG_WEAK void SYSCFG_DL_USER_QEI_0_init(void) {

    DL_TimerG_setClockConfig(
        USER_QEI_0_INST, (DL_TimerG_ClockConfig *) &gUSER_QEI_0ClockConfig);

    DL_TimerG_configQEI(USER_QEI_0_INST, DL_TIMER_QEI_MODE_2_INPUT,
        DL_TIMER_CC_INPUT_INV_NOINVERT, DL_TIMER_CC_0_INDEX);
    DL_TimerG_configQEI(USER_QEI_0_INST, DL_TIMER_QEI_MODE_2_INPUT,
        DL_TIMER_CC_INPUT_INV_NOINVERT, DL_TIMER_CC_1_INDEX);
    DL_TimerG_setLoadValue(USER_QEI_0_INST, 65535);
    DL_TimerG_enableClock(USER_QEI_0_INST);
    DL_TimerG_startCounter(USER_QEI_0_INST);
}



/*
 * Timer clock configuration to be sourced by BUSCLK /  (32000000 Hz)
 * timerClkFreq = (timerClkSrc / (timerClkDivRatio * (timerClkPrescale + 1)))
 *   32000000 Hz = 32000000 Hz / (1 * (0 + 1))
 */
static const DL_TimerA_ClockConfig gTIMER_A1ClockConfig = {
    .clockSel    = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_1,
    .prescale    = 0U,
};

/*
 * Timer load value (where the counter starts from) is calculated as (timerPeriod * timerClockFreq) - 1
 * TIMER_A1_INST_LOAD_VALUE = (32 us * 32000000 Hz) - 1
 */
static const DL_TimerA_TimerConfig gTIMER_A1TimerConfig = {
    .period     = TIMER_A1_INST_LOAD_VALUE,
    .timerMode  = DL_TIMER_TIMER_MODE_PERIODIC,
    .startTimer = DL_TIMER_START,
};

SYSCONFIG_WEAK void SYSCFG_DL_TIMER_A1_init(void) {

    DL_TimerA_setClockConfig(TIMER_A1_INST,
        (DL_TimerA_ClockConfig *) &gTIMER_A1ClockConfig);

    DL_TimerA_initTimerMode(TIMER_A1_INST,
        (DL_TimerA_TimerConfig *) &gTIMER_A1TimerConfig);
    DL_TimerA_enableClock(TIMER_A1_INST);





}


static const DL_UART_Main_ClockConfig gUSER_UART0ClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_BUSCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gUSER_UART0Config = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

SYSCONFIG_WEAK void SYSCFG_DL_USER_UART0_init(void)
{
    DL_UART_Main_setClockConfig(USER_UART0_INST, (DL_UART_Main_ClockConfig *) &gUSER_UART0ClockConfig);

    DL_UART_Main_init(USER_UART0_INST, (DL_UART_Main_Config *) &gUSER_UART0Config);
    /*
     * Configure baud rate by setting oversampling and baud rate divisors.
     *  Target baud rate: 921600
     *  Actual baud rate: 920863.31
     */
    DL_UART_Main_setOversampling(USER_UART0_INST, DL_UART_OVERSAMPLING_RATE_16X);
    DL_UART_Main_setBaudRateDivisor(USER_UART0_INST, USER_UART0_IBRD_32_MHZ_921600_BAUD, USER_UART0_FBRD_32_MHZ_921600_BAUD);


    /* Configure Interrupts */
    DL_UART_Main_enableInterrupt(USER_UART0_INST,
                                 DL_UART_MAIN_INTERRUPT_DMA_DONE_RX);

    /* Configure DMA Receive Event */
    DL_UART_Main_enableDMAReceiveEvent(USER_UART0_INST, DL_UART_DMA_INTERRUPT_RX);
    /* Configure FIFOs */
    DL_UART_Main_enableFIFOs(USER_UART0_INST);
    DL_UART_Main_setRXFIFOThreshold(USER_UART0_INST, DL_UART_RX_FIFO_LEVEL_ONE_ENTRY);
    DL_UART_Main_setTXFIFOThreshold(USER_UART0_INST, DL_UART_TX_FIFO_LEVEL_1_2_EMPTY);

    DL_UART_Main_enable(USER_UART0_INST);
}

/* USER_ADC_MOTOR_V Initialization */
static const DL_ADC12_ClockConfig gUSER_ADC_MOTOR_VClockConfig = {
    .clockSel       = DL_ADC12_CLOCK_SYSOSC,
    .divideRatio    = DL_ADC12_CLOCK_DIVIDE_1,
    .freqRange      = DL_ADC12_CLOCK_FREQ_RANGE_24_TO_32,
};
SYSCONFIG_WEAK void SYSCFG_DL_USER_ADC_MOTOR_V_init(void)
{
    DL_ADC12_setClockConfig(USER_ADC_MOTOR_V_INST, (DL_ADC12_ClockConfig *) &gUSER_ADC_MOTOR_VClockConfig);
    DL_ADC12_initSingleSample(USER_ADC_MOTOR_V_INST,
        DL_ADC12_REPEAT_MODE_ENABLED, DL_ADC12_SAMPLING_SOURCE_AUTO, DL_ADC12_TRIG_SRC_SOFTWARE,
        DL_ADC12_SAMP_CONV_RES_12_BIT, DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);
    DL_ADC12_configConversionMem(USER_ADC_MOTOR_V_INST, USER_ADC_MOTOR_V_ADCMEM_0,
        DL_ADC12_INPUT_CHAN_2, DL_ADC12_REFERENCE_VOLTAGE_VDDA, DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT, DL_ADC12_WINDOWS_COMP_MODE_DISABLED);
    DL_ADC12_enableFIFO(USER_ADC_MOTOR_V_INST);
    DL_ADC12_setPowerDownMode(USER_ADC_MOTOR_V_INST,DL_ADC12_POWER_DOWN_MODE_MANUAL);
    DL_ADC12_setSampleTime0(USER_ADC_MOTOR_V_INST,2);
    DL_ADC12_enableDMA(USER_ADC_MOTOR_V_INST);
    DL_ADC12_setDMASamplesCnt(USER_ADC_MOTOR_V_INST,6);
    DL_ADC12_enableDMATrigger(USER_ADC_MOTOR_V_INST,(DL_ADC12_DMA_MEM0_RESULT_LOADED));
    /* Enable ADC12 interrupt */
    DL_ADC12_clearInterruptStatus(USER_ADC_MOTOR_V_INST,(DL_ADC12_INTERRUPT_DMA_DONE));
    DL_ADC12_enableInterrupt(USER_ADC_MOTOR_V_INST,(DL_ADC12_INTERRUPT_DMA_DONE));
    DL_ADC12_enableConversions(USER_ADC_MOTOR_V_INST);
}
/* USER_ADC_OSCILLOSCOPE Initialization */
static const DL_ADC12_ClockConfig gUSER_ADC_OSCILLOSCOPEClockConfig = {
    .clockSel       = DL_ADC12_CLOCK_SYSOSC,
    .divideRatio    = DL_ADC12_CLOCK_DIVIDE_1,
    .freqRange      = DL_ADC12_CLOCK_FREQ_RANGE_24_TO_32,
};
SYSCONFIG_WEAK void SYSCFG_DL_USER_ADC_OSCILLOSCOPE_init(void)
{
    DL_ADC12_setClockConfig(USER_ADC_OSCILLOSCOPE_INST, (DL_ADC12_ClockConfig *) &gUSER_ADC_OSCILLOSCOPEClockConfig);
    DL_ADC12_initSingleSample(USER_ADC_OSCILLOSCOPE_INST,
        DL_ADC12_REPEAT_MODE_ENABLED, DL_ADC12_SAMPLING_SOURCE_AUTO, DL_ADC12_TRIG_SRC_SOFTWARE,
        DL_ADC12_SAMP_CONV_RES_12_BIT, DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);
    DL_ADC12_configConversionMem(USER_ADC_OSCILLOSCOPE_INST, USER_ADC_OSCILLOSCOPE_ADCMEM_0,
        DL_ADC12_INPUT_CHAN_1, DL_ADC12_REFERENCE_VOLTAGE_VDDA, DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT, DL_ADC12_WINDOWS_COMP_MODE_DISABLED);
    DL_ADC12_setPowerDownMode(USER_ADC_OSCILLOSCOPE_INST,DL_ADC12_POWER_DOWN_MODE_MANUAL);
    DL_ADC12_setSampleTime0(USER_ADC_OSCILLOSCOPE_INST,1024);
    DL_ADC12_enableDMA(USER_ADC_OSCILLOSCOPE_INST);
    DL_ADC12_setDMASamplesCnt(USER_ADC_OSCILLOSCOPE_INST,6);
    DL_ADC12_enableConversions(USER_ADC_OSCILLOSCOPE_INST);
}

static const DL_DMA_Config gDMA_CH1Config = {
    .transferMode   = DL_DMA_FULL_CH_REPEAT_SINGLE_TRANSFER_MODE,
    .extendedMode   = DL_DMA_NORMAL_MODE,
    .destIncrement  = DL_DMA_ADDR_INCREMENT,
    .srcIncrement   = DL_DMA_ADDR_UNCHANGED,
    .destWidth      = DL_DMA_WIDTH_HALF_WORD,
    .srcWidth       = DL_DMA_WIDTH_HALF_WORD,
    .trigger        = USER_ADC_MOTOR_V_INST_DMA_TRIGGER,
    .triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL,
};

SYSCONFIG_WEAK void SYSCFG_DL_DMA_CH1_init(void)
{
    DL_DMA_initChannel(DMA, DMA_CH1_CHAN_ID , (DL_DMA_Config *) &gDMA_CH1Config);
}
static const DL_DMA_Config gDMA_CH2Config = {
    .transferMode   = DL_DMA_FULL_CH_REPEAT_BLOCK_TRANSFER_MODE,
    .extendedMode   = DL_DMA_NORMAL_MODE,
    .destIncrement  = DL_DMA_ADDR_INCREMENT,
    .srcIncrement   = DL_DMA_ADDR_UNCHANGED,
    .destWidth      = DL_DMA_WIDTH_HALF_WORD,
    .srcWidth       = DL_DMA_WIDTH_HALF_WORD,
    .trigger        = USER_ADC_OSCILLOSCOPE_INST_DMA_TRIGGER,
    .triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL,
};

SYSCONFIG_WEAK void SYSCFG_DL_DMA_CH2_init(void)
{
    DL_DMA_initChannel(DMA, DMA_CH2_CHAN_ID , (DL_DMA_Config *) &gDMA_CH2Config);
}
static const DL_DMA_Config gDMA_CH0Config = {
    .transferMode   = DL_DMA_SINGLE_TRANSFER_MODE,
    .extendedMode   = DL_DMA_NORMAL_MODE,
    .destIncrement  = DL_DMA_ADDR_INCREMENT,
    .srcIncrement   = DL_DMA_ADDR_UNCHANGED,
    .destWidth      = DL_DMA_WIDTH_WORD,
    .srcWidth       = DL_DMA_WIDTH_WORD,
    .trigger        = USER_UART0_INST_DMA_TRIGGER,
    .triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL,
};

SYSCONFIG_WEAK void SYSCFG_DL_DMA_CH0_init(void)
{
    DL_DMA_initChannel(DMA, DMA_CH0_CHAN_ID , (DL_DMA_Config *) &gDMA_CH0Config);
}
SYSCONFIG_WEAK void SYSCFG_DL_DMA_init(void){
    SYSCFG_DL_DMA_CH1_init();
    SYSCFG_DL_DMA_CH2_init();
    SYSCFG_DL_DMA_CH0_init();
}


