/**
 * @file send_info.c
 * @brief 实现发送调试信息
 */

#include "send_info.h"
#include "app_types.h"
#include "ti_msp_dl_config.h"

int previous_motor_speed = 0; // 用于跟踪电机速度变化
int last_position = 0;

// 编码器相关变量
volatile int32_t encoder_position = 0;
volatile int32_t encoder_speed = 0;
volatile uint8_t encoder_direction = QEI_DIR_FWD;
volatile uint32_t encoder_update_time = 0;
volatile uint32_t debug_update_time = 0;

/**
 * @brief 发送当前电机速度信息
 * 优化版本：添加更多有用信息
 */
void Send_Motor_Speed(void) {
    // 计算电机理论转速 (基于设定速度)
    int32_t theoretical_rpm = (motor_speed * MAX_MOTOR_RPM) / 100;

    sprintf(uart_buffer, "电机转速: 设定值=%d%%, 理论RPM=%ld\r\n",
            motor_speed, (long)theoretical_rpm);

    UART_SendString(uart_buffer);
}

/**
 * @brief 发送编码器数据
 * 优化版本：添加实际转速计算，使用常量定义
 */
void Send_Encoder_Data(void) {
    // 计算实际电机转速 (RPM)
    int32_t actual_rpm = (encoder_speed * 60) / PULSES_PER_REVOLUTION;

    // 格式化编码器信息字符串 - 包含位置、脉冲速度、实际转速和方向
    sprintf(uart_buffer, "Encoder: 位置=%ld, 脉冲速度=%ld, 实际转速=%ld, 方向=%s\r\n",
            (long)encoder_position,
            (long)encoder_speed,
            (long)actual_rpm,
            (encoder_direction == QEI_DIR_FWD) ? "FWD" : "REV");

    UART_SendString(uart_buffer);
}

/**
 * @brief 发送当前电机状态信息至PC
 * 位置，转速，方向
 */
void Send_Motordebug_info(void) {
    static uint32_t last_debug_time = 0;
    uint32_t current_time = get_tick_ms();
    bool should_send_encoder = false;

    // 检查电机速度是否变化 - 立即发送
    if (motor_speed != previous_motor_speed) {
        Send_Motor_Speed();
        previous_motor_speed = motor_speed;
    }

    // 定期更新编码器数据 - 避免频繁更新
    if (current_time - last_debug_time >= ENCODER_UPDATE_INTERVAL) {
        // 一次性获取所有编码器数据
        encoder_speed = QEI_GetSpeed();
        encoder_position = QEI_GetPosition();
        encoder_direction = QEI_GetDirection();

        // 发送编码器数据
        Send_Encoder_Data();

        // 更新时间戳
        last_debug_time = current_time;
    }
}

/**
 * @brief 发送调试信息至串口屏
 */
void Send_Debug_Info_Port(void) {
    // 一次性获取所有编码器数据，减少函数调用开销
    encoder_speed = QEI_GetSpeed();
    encoder_position = QEI_GetPosition();
    encoder_direction = QEI_GetDirection();

    // 计算电机转速 (RPM) - 编码器脉冲速度转换为电机轴转速
    // 公式: RPM = (脉冲速度 * 60秒) / (编码器线数 * 减速比 * QEI倍频)
    int32_t motor_rpm = (encoder_speed * 60) / PULSES_PER_REVOLUTION;

    // 发送电机设定速度百分比
    sprintf(uart_buffer, "t1.txt=\"%d%%\"", motor_speed);
    Send_String_With_End(uart_buffer);

    // 发送编码器实际脉冲速度
    sprintf(uart_buffer, "t2.txt=\"%ld\"", (long)encoder_speed);
    Send_String_With_End(uart_buffer);

    // 发送编码器位置
    sprintf(uart_buffer, "n2_bmq.val=%ld", (long)encoder_position);
    Send_String_With_End(uart_buffer);

    // 发送电机实际转速 (RPM)
    sprintf(uart_buffer, "n3_rpm.val=%ld", (long)motor_rpm);
    Send_String_With_End(uart_buffer);

    // 发送电压信息
    sprintf(uart_buffer, "x0_i.val=%ld", (long)adc_value*3300/4096);
    Send_String_With_End(uart_buffer);

    // 更新上次位置记录
    last_position = encoder_position;
}