/**
 * @file send_info.c
 * @brief 实现发送调试信息
 */

#include "send_info.h"
#include "app_types.h"
#include "ti_msp_dl_config.h"

int previous_motor_speed = 0; // 用于跟踪电机速度变化
int last_position = 0;

// 编码器相关变量
volatile int32_t encoder_position = 0;
volatile int32_t encoder_speed = 0;
volatile uint8_t encoder_direction = QEI_DIR_FWD;
volatile uint32_t encoder_update_time = 0;
volatile uint32_t debug_update_time = 0;

/*************************************************************************************************************
**************************************************电机部分*****************************************************
*************************************************************************************************************/

/**
 * @brief 发送当前电机速度信息
 * 优化版本：添加更多有用信息
 */
void Send_Motor_Speed(void) {
    // 计算电机理论转速 (基于设定速度)
    int32_t theoretical_rpm = (motor_speed * MAX_MOTOR_RPM) / 100;

    sprintf(uart_buffer, "电机转速: 设定值=%d%%, 理论RPM=%ld\r\n",
            motor_speed, (long)theoretical_rpm);

    UART_SendString(uart_buffer);
}

/**
 * @brief 发送编码器数据
 * 优化版本：添加实际转速计算，使用常量定义
 */
void Send_Encoder_Data(void) {
    // 计算实际电机转速 (RPM)
    int32_t actual_rpm = (encoder_speed * 60) / PULSES_PER_REVOLUTION;

    // 格式化编码器信息字符串 - 包含位置、脉冲速度、实际转速和方向
    sprintf(uart_buffer, "Encoder: 位置=%ld, 脉冲速度=%ld, 实际转速=%ld, 方向=%s\r\n",
            (long)encoder_position,
            (long)encoder_speed,
            (long)actual_rpm,
            (encoder_direction == QEI_DIR_FWD) ? "FWD" : "REV");

    UART_SendString(uart_buffer);
}

/**
 * @brief 发送当前电机状态信息至PC
 * 位置，转速，方向
 */
void Send_Motordebug_info(void) {
    static uint32_t last_debug_time = 0;
    uint32_t current_time = get_tick_ms();
    bool should_send_encoder = false;

    // 检查电机速度是否变化 - 立即发送
    if (motor_speed != previous_motor_speed) {
        Send_Motor_Speed();
        previous_motor_speed = motor_speed;
    }

    // 定期更新编码器数据 - 避免频繁更新
    if (current_time - last_debug_time >= ENCODER_UPDATE_INTERVAL) {
        // 一次性获取所有编码器数据
        encoder_speed = QEI_GetSpeed();
        encoder_position = QEI_GetPosition();
        encoder_direction = QEI_GetDirection();

        // 发送编码器数据
        Send_Encoder_Data();

        // 更新时间戳
        last_debug_time = current_time;
    }
}

/**
 * @brief 发送调试信息至串口屏
 */
void Send_Debug_Info_Port(void) {
    // 一次性获取所有编码器数据，减少函数调用开销
    encoder_speed = QEI_GetSpeed();
    encoder_position = QEI_GetPosition();
    encoder_direction = QEI_GetDirection();

    // 计算电机转速 (RPM) - 编码器脉冲速度转换为电机轴转速
    // 公式: RPM = (脉冲速度 * 60秒) / (编码器线数 * 减速比 * QEI倍频)
    int32_t motor_rpm = (encoder_speed * 60) / PULSES_PER_REVOLUTION;

    // 发送电机设定速度百分比
    sprintf(uart_buffer, "t1.txt=\"%d%%\"", motor_speed);
    Send_String_With_End(uart_buffer);

    // 发送编码器实际脉冲速度
    sprintf(uart_buffer, "t2.txt=\"%ld\"", (long)encoder_speed);
    Send_String_With_End(uart_buffer);

    // 发送编码器位置
    sprintf(uart_buffer, "n2_bmq.val=%ld", (long)encoder_position);
    Send_String_With_End(uart_buffer);

    // 发送电机实际转速 (RPM)
    sprintf(uart_buffer, "n3_rpm.val=%ld", (long)motor_rpm);
    Send_String_With_End(uart_buffer);

    // 发送电压信息
    sprintf(uart_buffer, "x0_i.val=%ld", (long)adc_value*3300/4096);
    Send_String_With_End(uart_buffer);

    // 更新上次位置记录
    last_position = encoder_position;
}

/*************************************************************************************************************
************************************************示波器部分*****************************************************
*************************************************************************************************************/

/**
 * @brief 发送波形数据到上位机
 */
void Oscilloscope_SendWaveformData(void)
{
    // 检查是否有有效数据
    if (!osc_config.data_ready) {
        return;
    }

    // 抽取显示数据（从200点抽取到150点）
    int step = OSC_BUFFER_SIZE / OSC_DISPLAY_POINTS;
    if (step < 1) step = 1;

    for (int i = 0; i < OSC_DISPLAY_POINTS && i * step < OSC_BUFFER_SIZE; i++) {
        osc_config.display_buffer[i] = osc_config.ch1_buffer[i * step];
    }

    // 规范化波形数据发送给上位机
    // 显示规格：355像素高度，6行格子，每格60像素
    // 根据TJC文档：add objectid,channel,data

    // 分批发送波形数据点，避免阻塞
    const int BATCH_SIZE = 20;  // 减少批量大小，提高稳定性

    for (int batch = 0; batch < OSC_DISPLAY_POINTS; batch += BATCH_SIZE) {
        for (int i = batch; i < batch + BATCH_SIZE && i < OSC_DISPLAY_POINTS; i++) {
            // 将ADC值(0-4095)转换为显示坐标(0-255)
            // 坐标系：左下角为(0)，右上角为(255)
            // 高电压 → 大ADC值 → 大Y坐标值（上方）
            // 低电压 → 小ADC值 → 小Y坐标值（下方）

            // 正确的电压到像素转换公式
            // 1. ADC转电压: voltage = ADC * 3.3 / 4095
            // 2. 电压转像素: pixels = voltage / voltage_scale * OSC_PATTERN_HEIGHT
            // 3. 上移两格偏移: +120像素 / 缩放系数OSC_REALY_HEIGHT / OSC_DISPLAY_HEIGHT (可显示负数)
            float voltage = (osc_config.display_buffer[i] * 3.3f) / 4095.0f;
            float pixels = voltage / osc_config.ch1.voltage_scale * OSC_PATTERN_HEIGHT;
            uint16_t display_value = (uint16_t)((pixels + 120) / (double)(OSC_REALY_HEIGHT / OSC_DISPLAY_HEIGHT));

            // 确保在有效范围内
            if (display_value > OSC_DISPLAY_HEIGHT) {
                display_value = OSC_DISPLAY_HEIGHT;
            }

            sprintf(uart_buffer, "add %s,%d,%d", OSC_WAVEFORM_OBJECT, OSC_WAVEFORM_CHANNEL, display_value);
            Send_String_With_End(uart_buffer);
        }

        // 每批之间小延时，让上位机有时间处理
        delay_ms(1); // 尽可能小,提高响应速度
    }

    // 发送完成后刷新显示
    sprintf(uart_buffer, "ref %s", OSC_WAVEFORM_OBJECT);
    Send_String_With_End(uart_buffer);
}

/**
 * @brief 发送测量结果到上位机
 */
void Oscilloscope_SendMeasurement(void)
{
    if (!osc_config.measurement.valid) {
        // 发送无效数据标识
        sprintf(uart_buffer, "t_forquen.txt=\"--\"");
        Send_String_With_End(uart_buffer);
        sprintf(uart_buffer, "t_max_max.txt=\"--\"");
        Send_String_With_End(uart_buffer);
        return;
    }

    // 按照要求.md格式发送测量结果
    // 1：频率 t_forquen.txt=
    sprintf(uart_buffer, "t_forquen.txt=\"%.2fHz\"", osc_config.measurement.frequency);
    Send_String_With_End(uart_buffer);

    // 2：峰峰值 t_max_max.txt=
    sprintf(uart_buffer, "t_max_max.txt=\"%.3fV\"", osc_config.measurement.peak_to_peak);
    Send_String_With_End(uart_buffer);

}

/**
 * @brief 发送示波器状态到上位机
 */
void Oscilloscope_SendStatus(void)
{
    // 按照要求.md格式发送示波器配置信息
    // 注意：波形数据单独发送，减少上位机压力

    // 通道设置
    // 1：ch1选取状态 t_ch1.txt=
    sprintf(uart_buffer, "t_ch1.txt=\"%s\"", osc_config.ch1.enabled ? "ON" : "OFF");
    Send_String_With_End(uart_buffer);

    // 2：ch1电压/格 t_v_ch1.txt=
    sprintf(uart_buffer, "t_v_ch1.txt=\"%.2fV\"", osc_config.ch1.voltage_scale);
    Send_String_With_End(uart_buffer);

    // 3：ch2选取状态 t_ch2.txt=
    sprintf(uart_buffer, "t_ch2.txt=\"%s\"", osc_config.ch2.enabled ? "ON" : "OFF");
    Send_String_With_End(uart_buffer);

    // 4：ch2电压/格 t_v_ch2.txt=
    sprintf(uart_buffer, "t_v_ch2.txt=\"%.2fV\"", osc_config.ch2.voltage_scale);
    Send_String_With_End(uart_buffer);

    // 时基设置
    // 1：时间/格 t_time_pice.txt=
    sprintf(uart_buffer, "t_time_pice.txt=\"%.3fms\"", osc_config.timebase.time_per_div * 1000);
    Send_String_With_End(uart_buffer);

    // 触发设置
    // 1：触发模式 t_mode.txt=
    const char* trigger_mode;
    switch (osc_config.trigger.mode) {
        case OSC_TRIGGER_AUTO:
            trigger_mode = "AUTO";
            break;
        case OSC_TRIGGER_NORMAL:
            trigger_mode = "NORMAL";
            break;
        case OSC_TRIGGER_SINGLE:
            trigger_mode = "SINGLE";
            break;
        default:
            trigger_mode = "UNKNOWN";
            break;
    }
    sprintf(uart_buffer, "t_mode.txt=\"%s\"", trigger_mode);
    Send_String_With_End(uart_buffer);

    // 2：触发电平 t_v_min.txt=
    sprintf(uart_buffer, "t_v_min.txt=\"%.3fV\"", osc_config.trigger.level/1000);
    Send_String_With_End(uart_buffer);

    sprintf(uart_buffer, "t0.txt=\"V:%.2f\"",osc_config.measurement.mean);
    Send_String_With_End(uart_buffer);

    // // 发送调试信息
    // sprintf(uart_buffer, "t0.txt=\"DMA COUNT:%d\"", osc_dma_interrupt_count);
    // Send_String_With_End(uart_buffer);    

    Oscilloscope_SendMeasurement();
}
