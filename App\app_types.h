/*
 * app_types.h
 * 基本类型和定义，用于避免包含链接错误
 */

#ifndef APP_TYPES_H_
#define APP_TYPES_H_

/**
 * @brief 发送串口屏结束符
 */
static inline void Port_End(void) {
    // 使用静态数组避免重复分配
    static const uint8_t end_bytes[3] = {0xFF, 0xFF, 0xFF};
    UART_SendBuffer(end_bytes, 3);
}

/**
 * @brief 发送带结束符的字符串到串口屏
 * @param str 要发送的字符串
 */
static void Send_String_With_End(const char* str) {
    UART_SendString(str);
    Port_End();
}

#endif