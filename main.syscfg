/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const ADC122 = ADC12.addInstance();
const DMA    = scripting.addModule("/ti/driverlib/DMA");
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const QEI    = scripting.addModule("/ti/driverlib/QEI", {}, false);
const QEI1   = QEI.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
ADC121.$name                          = "USER_ADC_MOTOR_V";
ADC121.adcMem0chansel                 = "DL_ADC12_INPUT_CHAN_2";
ADC121.powerDownMode                  = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.repeatMode                     = true;
ADC121.configureDMA                   = true;
ADC121.enableFIFO                     = true;
ADC121.sampleTime0                    = "62.5 ns";
ADC121.enabledDMATriggers             = ["DL_ADC12_DMA_MEM0_RESULT_LOADED"];
ADC121.enabledInterrupts              = ["DL_ADC12_INTERRUPT_DMA_DONE"];
ADC121.sampCnt                        = 6;
ADC121.peripheral.$assign             = "ADC0";
ADC121.peripheral.adcPin2.$assign     = "PA25";
ADC121.adcPin2Config.$name            = "ti_driverlib_gpio_GPIOPinGeneric2";
ADC121.DMA_CHANNEL.$name              = "DMA_CH1";
ADC121.DMA_CHANNEL.addressMode        = "f2b";
ADC121.DMA_CHANNEL.srcLength          = "HALF_WORD";
ADC121.DMA_CHANNEL.dstLength          = "HALF_WORD";
ADC121.DMA_CHANNEL.transferMode       = "FULL_CH_REPEAT_SINGLE";
ADC121.DMA_CHANNEL.peripheral.$assign = "DMA_CH1";

ADC122.$name                          = "USER_ADC_OSCILLOSCOPE";
ADC122.adcMem0chansel                 = "DL_ADC12_INPUT_CHAN_1";
ADC122.powerDownMode                  = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC122.repeatMode                     = true;
ADC122.configureDMA                   = true;
ADC122.sampleTime0                    = "32 us";
ADC122.sampCnt                        = 6;
ADC122.peripheral.$assign             = "ADC1";
ADC122.peripheral.adcPin1.$assign     = "PA16";
ADC122.adcPin1Config.$name            = "ti_driverlib_gpio_GPIOPinGeneric8";
ADC122.adcPin1Config.enableConfig     = true;
ADC122.DMA_CHANNEL.$name              = "DMA_CH2";
ADC122.DMA_CHANNEL.fillIncrement      = "INCREMENT";
ADC122.DMA_CHANNEL.addressMode        = "f2b";
ADC122.DMA_CHANNEL.srcLength          = "HALF_WORD";
ADC122.DMA_CHANNEL.dstLength          = "HALF_WORD";
ADC122.DMA_CHANNEL.transferMode       = "FULL_CH_REPEAT_BLOCK";
ADC122.DMA_CHANNEL.peripheral.$assign = "DMA_CH2";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.port                               = "PORTA";
GPIO1.$name                              = "USER_LED_1";
GPIO1.associatedPins[0].internalResistor = "PULL_UP";
GPIO1.associatedPins[0].assignedPin      = "0";
GPIO1.associatedPins[0].$name            = "PIN_A_0";

GPIO2.$name                              = "USER_SWITCH_2";
GPIO2.port                               = "PORTB";
GPIO2.associatedPins[0].$name            = "PIN_B_21";
GPIO2.associatedPins[0].assignedPin      = "21";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].direction        = "INPUT";

PWM1.$name                              = "PWM_MOTOER_A";
PWM1.dutyArgs                           = "[50,0,0,0]";
PWM1.timerCount                         = 32000;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.peripheral.ccp0Pin.$assign         = "PA12";
PWM1.peripheral.ccp1Pin.$assign         = "PA13";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 0;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

QEI1.clockDivider                       = 8;
QEI1.clockPrescale                      = 200;
QEI1.$name                              = "USER_QEI_0";
QEI1.timerStartTimer                    = true;
QEI1.peripheral.ccp0Pin.$assign         = "PB6";
QEI1.peripheral.ccp1Pin.$assign         = "PB16";
QEI1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
QEI1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
QEI1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
QEI1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
QEI1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
QEI1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
QEI1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
QEI1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

TIMER1.$name           = "TIMER_A1";
TIMER1.timerMode       = "PERIODIC";
TIMER1.timerStartTimer = true;
TIMER1.timerPeriod     = "32 us";

UART1.$name                            = "USER_UART0";
UART1.enableFIFO                       = true;
UART1.rxFifoThreshold                  = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART1.enabledInterrupts                = ["DMA_DONE_RX"];
UART1.enabledDMARXTriggers             = "DL_UART_DMA_INTERRUPT_RX";
UART1.targetBaudRate                   = 921600;
UART1.peripheral.rxPin.$assign         = "PA11";
UART1.peripheral.txPin.$assign         = "PA10";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART1.DMA_CHANNEL_RX.$name             = "DMA_CH0";
UART1.DMA_CHANNEL_RX.addressMode       = "f2b";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution                = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution       = "PA20";
Board.peripheral.swdioPin.$suggestSolution       = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution     = "PA0";
GPIO2.associatedPins[0].pin.$suggestSolution     = "PB21";
QEI1.peripheral.$suggestSolution                 = "TIMG8";
TIMER1.peripheral.$suggestSolution               = "TIMA0";
UART1.peripheral.$suggestSolution                = "UART0";
UART1.DMA_CHANNEL_RX.peripheral.$suggestSolution = "DMA_CH0";
