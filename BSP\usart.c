/*
 * usart.c
 * 串口模块化处理实现文件
 */

#include "usart.h"
#include "tjc.h"

// 添加缺失的声明
extern void TJC_FrameReceivedCallback(const TJC_Frame* frame);
extern TJC_Parser tjc_parser;

// 接收缓冲区
static uint8_t rxBuffer[UART_RX_BUFFER_SIZE];
static uint16_t rxHead = 0;
static uint16_t rxTail = 0;
static bool rxBufferOverflow = false;

// 发送缓冲区
static uint8_t txBuffer[UART_TX_BUFFER_SIZE];
static uint16_t txHead = 0;
static uint16_t txTail = 0;
static bool txBusy = false;

// DMA接收缓冲区
volatile uint8_t gRxPacket[UART_PACKET_SIZE];
// 用户数据处理缓冲区 
static uint8_t rxProcessBuffer[UART_DMA_RX_BUFFER_SIZE];
static uint16_t rxProcessIndex = 0;
static uint16_t rxAvailableBytes = 0;

// 数据接收完成标志
volatile bool gRxComplete = false;

// 调试计数器
static uint32_t dmaCompleteCount = 0;
static uint32_t bytesProcessedCount = 0;

// 超时处理相关变量
static uint32_t lastDmaTransferSize = UART_PACKET_SIZE;
static uint32_t stableCount = 0;

/**
 * @brief 初始化UART和DMA
 * 
 * @param config UART配置参数，传NULL则使用默认配置
 * @return UART_Status_t 初始化状态
 */
UART_Status_t UART_Init(UART_Config_t *config) {
    // 初始化变量
    rxHead = 0;
    rxTail = 0;
    txHead = 0;
    txTail = 0;
    rxBufferOverflow = false;
    txBusy = false;
    rxProcessIndex = 0;
    rxAvailableBytes = 0;
    gRxComplete = false;

    // 重新配置DMA通道以使用正确的数据宽度（字节而不是字）
    DL_DMA_Config dmaConfig = {
        .transferMode   = DL_DMA_SINGLE_TRANSFER_MODE,
        .extendedMode   = DL_DMA_NORMAL_MODE,
        .destIncrement  = DL_DMA_ADDR_INCREMENT,
        .srcIncrement   = DL_DMA_ADDR_UNCHANGED,
        .destWidth      = DL_DMA_WIDTH_BYTE,  // 修改为字节宽度
        .srcWidth       = DL_DMA_WIDTH_BYTE,  // 修改为字节宽度
        .trigger        = USER_UART0_INST_DMA_TRIGGER,
        .triggerType    = DL_DMA_TRIGGER_TYPE_EXTERNAL,
    };

    // 重新初始化DMA通道
    DL_DMA_initChannel(DMA, DMA_CH0_CHAN_ID, &dmaConfig);

    // 配置DMA接收
    UART_ConfigDmaRx(gRxPacket, UART_PACKET_SIZE);

    // 启用UART中断
    NVIC_EnableIRQ(USER_UART0_INST_INT_IRQN);

    // 启用接收和发送中断
    DL_UART_enableInterrupt(USER_UART0_INST, DL_UART_INTERRUPT_DMA_DONE_RX);

    // 发送一条初始化完成消息
    UART_SendString("UART+DMA initialized with BYTE width\r\n");

    return UART_OK;
}

/**
 * @brief 配置DMA接收
 * 
 * @param rxBuffer 接收缓冲区
 * @param size 接收大小
 */
void UART_ConfigDmaRx(volatile uint8_t *rxBuffer, uint16_t size) {
    // 配置DMA源地址、目标地址和传输大小
    DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)(&USER_UART0_INST->RXDATA));
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)rxBuffer);
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, size);
    
    // 启用DMA通道
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);
    
    // 确认DMA通道已启用
    while (false == DL_DMA_isChannelEnabled(DMA, DMA_CH0_CHAN_ID)) {
        // 等待DMA通道启用
    }
}

/**
 * @brief 将一个字节添加到发送缓冲区
 * 
 * @param data 要添加的数据
 * @return true 添加成功
 * @return false 缓冲区已满
 */
static bool UART_AddToTxBuffer(uint8_t data) {
    uint16_t nextHead = (txHead + 1) % UART_TX_BUFFER_SIZE;
    
    if (nextHead == txTail) {
        // 缓冲区已满
        return false;
    }
    
    txBuffer[txHead] = data;
    txHead = nextHead;
    return true;
}

/**
 * @brief 从发送缓冲区获取一个字节
 * 
 * @param data 数据指针
 * @return true 获取成功
 * @return false 缓冲区为空
 */
static bool UART_GetFromTxBuffer(uint8_t *data) {
    if (txHead == txTail) {
        // 缓冲区为空
        return false;
    }
    
    *data = txBuffer[txTail];
    txTail = (txTail + 1) % UART_TX_BUFFER_SIZE;
    return true;
}

/**
 * @brief 将一个字节添加到接收缓冲区
 * 
 * @param data 要添加的数据
 * @return true 添加成功
 * @return false 缓冲区已满
 */
static bool UART_AddToRxBuffer(uint8_t data) {
    uint16_t nextHead = (rxHead + 1) % UART_RX_BUFFER_SIZE;
    
    if (nextHead == rxTail) {
        // 缓冲区已满
        rxBufferOverflow = true;
        return false;
    }
    
    rxBuffer[rxHead] = data;
    rxHead = nextHead;
    return true;
}

/**
 * @brief 从接收缓冲区获取一个字节
 * 
 * @param data 数据指针
 * @return true 获取成功
 * @return false 缓冲区为空
 */
static bool UART_GetFromRxBuffer(uint8_t *data) {
    if (rxHead == rxTail) {
        // 缓冲区为空
        return false;
    }
    
    *data = rxBuffer[rxTail];
    rxTail = (rxTail + 1) % UART_RX_BUFFER_SIZE;
    return true;
}

/**
 * @brief 检查UART是否有数据可读
 * 
 * @return bool 有数据返回true，否则返回false
 */
bool UART_IsDataAvailable(void) {
    return (rxAvailableBytes > 0);
}

/**
 * @brief 发送单个字节(阻塞方式)
 * 
 * @param data 要发送的数据
 * @return UART_Status_t 发送状态
 */
UART_Status_t UART_SendByte(uint8_t data) {
    // 直接发送，等待发送完成
    DL_UART_transmitData(USER_UART0_INST, data);
    
    // 等待发送完成
    while(!DL_UART_isTXFIFOEmpty(USER_UART0_INST)) {
        // 等待发送完成
    }
    
    return UART_OK;
}

/**
 * @brief 发送字符串(阻塞方式)
 * 
 * @param str 要发送的字符串
 * @return UART_Status_t 发送状态
 */
UART_Status_t UART_SendString(const char *str) {
    if (str == NULL) {
        return UART_ERROR;
    }
    
    while (*str != '\0') {
        UART_SendByte((uint8_t)*str);
        str++;
    }
    
    return UART_OK;
}

/**
 * @brief 发送数据缓冲区(阻塞方式)
 * 
 * @param data 数据缓冲区
 * @param size 数据大小
 * @return UART_Status_t 发送状态
 */
UART_Status_t UART_SendBuffer(const uint8_t *data, uint16_t size) {
    if (data == NULL || size == 0) {
        return UART_ERROR;
    }
    
    for (uint16_t i = 0; i < size; i++) {
        UART_SendByte(data[i]);
    }
    
    return UART_OK;
}

/**
 * @brief 接收单个字节
 * 
 * @param data 接收数据的指针
 * @return UART_Status_t 接收状态
 */
UART_Status_t UART_ReceiveByte(uint8_t *data) {
    if (data == NULL) {
        return UART_ERROR;
    }
    
    if (rxAvailableBytes == 0) {
        return UART_ERROR; // 没有数据可读
    }
    
    *data = rxProcessBuffer[rxProcessIndex++];
    rxAvailableBytes--;
    
    // 环形缓冲区处理
    if (rxProcessIndex >= UART_DMA_RX_BUFFER_SIZE) {
        rxProcessIndex = 0;
    }
    
    return UART_OK;
}

/**
 * @brief 获取接收到的最新字节
 * 
 * @return uint8_t 接收到的字节
 */
uint8_t UART_GetLastReceivedByte(void) {
    uint8_t data = 0;
    
    if (UART_ReceiveByte(&data) == UART_OK) {
        return data;
    }
    
    return 0;
}

/**
 * @brief 处理DMA接收到的数据
 * 当DMA完成接收后调用此函数处理接收到的数据
 */
void UART_ProcessReceivedData(void) {
    // 只有在DMA接收完成时才处理数据
    if (!gRxComplete) {  // 如果没有完成接收，则直接返回
        return;
    }

    // 计算实际接收到的字节数
    uint32_t currentTransferSize = DL_DMA_getTransferSize(DMA, DMA_CH0_CHAN_ID);
    uint16_t actualReceived = UART_PACKET_SIZE - currentTransferSize;

    // 如果DMA报告的接收字节数异常少，但缓冲区有数据，则扫描缓冲区
    if (actualReceived < 7 && gRxPacket[0] == 0x61) {
        // 扫描缓冲区找到实际的数据长度
        for (uint16_t i = 1; i < UART_PACKET_SIZE; i++) {
            if (gRxPacket[i] == 0xFF) {
                actualReceived = i + 1;  // 找到结束符，设置实际长度
                break;
            }
        }

        // 如果没找到结束符，但有明显的TJC协议特征，强制设置为7字节
        if (actualReceived < 7 && gRxPacket[1] == 0x00 && gRxPacket[2] == 0x01 && gRxPacket[3] == 0x02) {
            actualReceived = 7;
        }
    }

    // // 发送调试信息 - 显示接收到的原始数据
    // sprintf(debugBuffer, "DMA RX complete, received %d bytes (corrected), TransferSize=%lu: ",
    //         actualReceived, (unsigned long)currentTransferSize);
    // UART_SendString(debugBuffer);

    // 调试代码已移除

    // 清除之前的处理缓冲区数据，确保从干净状态开始
    rxAvailableBytes = 0;
    rxProcessIndex = 0;

    // 将DMA接收到的数据复制到处理缓冲区
    for (uint16_t i = 0; i < actualReceived; i++) {
        if (rxAvailableBytes < UART_DMA_RX_BUFFER_SIZE) {
            uint16_t writeIndex = (rxProcessIndex + rxAvailableBytes) % UART_DMA_RX_BUFFER_SIZE;
            rxProcessBuffer[writeIndex] = gRxPacket[i];
            rxAvailableBytes++;
        }
    }

    // // 调试输出：显示处理缓冲区状态
    // sprintf(debugBuffer, "Processing buffer: rxAvailableBytes=%d, rxProcessIndex=%d\r\n",
    //         rxAvailableBytes, rxProcessIndex);
    // UART_SendString(debugBuffer);

    // 强制清除DMA接收缓冲区，防止旧数据残留
    memset((void*)gRxPacket, 0x00, UART_PACKET_SIZE);

    // 停用DMA通道
    DL_DMA_disableChannel(DMA, DMA_CH0_CHAN_ID);

    // 等待一小段时间确保DMA停止
    for(volatile int i = 0; i < 1000; i++);

    // 重新配置DMA以接收更多数据
    UART_ConfigDmaRx(gRxPacket, UART_PACKET_SIZE);

    // 重新启用DMA通道
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    // 清除接收完成标志
    gRxComplete = false;
}

/**
 * @brief TJC协议解析数据处理
 * 处理所有可用的接收数据
 */
void UART_ProcessTJCData(void) {
    // 处理DMA接收到的数据
    if (gRxComplete) {  // 如果DMA接收完成，则处理数据
        UART_ProcessReceivedData();
    } else {
        // 检查DMA是否有部分数据接收（超时处理）
        uint32_t currentTransferSize = DL_DMA_getTransferSize(DMA, DMA_CH0_CHAN_ID);

        if (currentTransferSize != lastDmaTransferSize) {
            // 传输大小发生变化，重置稳定计数器
            lastDmaTransferSize = currentTransferSize;
            stableCount = 0;
        } else if (currentTransferSize < UART_PACKET_SIZE) {
            // 传输大小稳定且小于包大小，说明有部分数据
            stableCount++;

            // 如果稳定了3个周期（约300ms），强制处理数据
            if (stableCount >= 3) {
                // 手动触发数据处理
                gRxComplete = true;
                stableCount = 0;

                // char timeoutBuffer[50];
                // sprintf(timeoutBuffer, "Timeout trigger: %lu bytes\r\n",
                //         (unsigned long)(UART_PACKET_SIZE - currentTransferSize));
                // UART_SendString(timeoutBuffer);
            }
        }
    }

    // 检查是否有数据可读
    while (UART_IsDataAvailable()) {
        // 获取接收到的字节
        uint8_t receivedByte = UART_GetLastReceivedByte();

        // 将字节传递给TJC解析器
        TJC_ParseByte(&tjc_parser, receivedByte, TJC_FrameReceivedCallback);

        bytesProcessedCount++;
    }
}

/**
 * @brief 获取UART调试统计信息
 */
void UART_GetDebugStats(void) {
    char statsBuffer[150];

    // 获取DMA通道状态
    bool dmaEnabled = DL_DMA_isChannelEnabled(DMA, DMA_CH0_CHAN_ID);
    uint32_t dmaTransferSize = DL_DMA_getTransferSize(DMA, DMA_CH0_CHAN_ID);

}

/**
 * @brief UART中断处理函数
 */
void UART0_IRQHandler(void) {
    uint32_t status = DL_UART_getPendingInterrupt(USER_UART0_INST);

    if (status & DL_UART_INTERRUPT_DMA_DONE_RX) {
        gRxComplete = true;
        dmaCompleteCount++;
        
        // 清除中断标志
        DL_UART_clearInterruptStatus(USER_UART0_INST, DL_UART_INTERRUPT_DMA_DONE_RX);
    }
}