/**
 * @file oscilloscope.c
 * @brief 示波器功能模块实现
 * @version 1.0
 * @date 2025-07-17
 * 
 * 本文件实现了示波器的核心功能，包括数据采集、触发检测、测量等
 */

#include "oscilloscope.h"
#include "usart.h"
#include "app_types.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

/* 示波器配置实例 */
OscilloscopeConfig osc_config = {0};

/* 外部变量引用 */
extern char uart_buffer[1000];

/* DMA中断计数器（用于调试） */
volatile uint32_t osc_dma_interrupt_count = 0;

/* 私有变量 */
static uint32_t last_sample_time = 0;
static bool dma_ch1_complete = false;
static bool dma_ch2_complete = false;

/**
 * @brief 初始化示波器模块
 */
void Oscilloscope_Init(void)
{
    // 初始化配置结构
    memset(&osc_config, 0, sizeof(OscilloscopeConfig));
    
    // 设置默认配置
    osc_config.state = OSC_STATE_STOP;
    
    // 通道1默认配置
    osc_config.ch1.enabled = true;
    osc_config.ch1.voltage_scale = 1.0f;  // 1V/格
    osc_config.ch1.offset = 0.0f;
    osc_config.ch1.coupling = 0;  // DC耦合
    
    // 通道2默认配置
    osc_config.ch2.enabled = false;  // 暂时只启用CH1
    osc_config.ch2.voltage_scale = 1.0f;
    osc_config.ch2.offset = 0.0f;
    osc_config.ch2.coupling = 0;
    
    // 触发默认配置
    osc_config.trigger.mode = OSC_TRIGGER_AUTO;
    osc_config.trigger.edge = OSC_TRIGGER_RISING;
    osc_config.trigger.source = 0;  // CH1触发
    osc_config.trigger.level = 2048;  // 中间电平
    osc_config.trigger.holdoff = 100;
    
    // 时基默认配置
    osc_config.timebase.time_per_div = 0.001f;  // 1ms/格
    osc_config.timebase.sample_rate = OSC_SAMPLE_RATE_DEFAULT;
    osc_config.timebase.pre_trigger = OSC_BUFFER_SIZE / 4;  // 25%预触发
    
    // 初始化测量结果
    osc_config.measurement.valid = false;

    // 配置DMA缓冲区
    // 示波器使用独立的ADC1实例和DMA_CH2
    DL_DMA_setSrcAddr(DMA, DMA_CH2_CHAN_ID,
                      (uint32_t)DL_ADC12_getMemResultAddress(USER_ADC_OSCILLOSCOPE_INST,
                      USER_ADC_OSCILLOSCOPE_ADCMEM_0));
    DL_DMA_setDestAddr(DMA, DMA_CH2_CHAN_ID, (uint32_t)osc_config.ch1_buffer);
    DL_DMA_setTransferSize(DMA, DMA_CH2_CHAN_ID, OSC_BUFFER_SIZE);
    
    // 启用DMA中断
    DL_DMA_enableInterrupt(DMA, DL_DMA_INTERRUPT_CHANNEL2);
    
    // sprintf(uart_buffer, "示波器模块初始化完成\r\n");
    // UART_SendString(uart_buffer);
}

/**
 * @brief 启动示波器采集
 */
void Oscilloscope_Start(void)
{
    osc_config.state = OSC_STATE_RUN;
    osc_config.buffer_index = 0;
    osc_config.sample_count = 0;
    osc_config.data_ready = false;
    osc_config.ch1.enabled = true;

    // 重新配置DMA - 确保地址和大小正确
    DL_DMA_setSrcAddr(DMA, DMA_CH2_CHAN_ID,
                      (uint32_t)DL_ADC12_getMemResultAddress(USER_ADC_OSCILLOSCOPE_INST,
                      USER_ADC_OSCILLOSCOPE_ADCMEM_0));
    DL_DMA_setDestAddr(DMA, DMA_CH2_CHAN_ID, (uint32_t)osc_config.ch1_buffer);
    DL_DMA_setTransferSize(DMA, DMA_CH2_CHAN_ID, OSC_BUFFER_SIZE);

    // 启用DMA通道
    DL_DMA_enableChannel(DMA, DMA_CH2_CHAN_ID);

    // 启动采样定时器（这会触发ADC采样）
    DL_TimerA_startCounter(TIMER_A1_INST);

    // 启动ADC转换（重要：启动连续转换）
    DL_ADC12_startConversion(USER_ADC_OSCILLOSCOPE_INST);
}

/**
 * @brief 停止示波器采集
 */
void Oscilloscope_Stop(void)
{
    osc_config.state = OSC_STATE_STOP;
    osc_config.ch1.enabled = false;

    // 停止ADC和DMA
    DL_ADC12_stopConversion(USER_ADC_OSCILLOSCOPE_INST);
    DL_DMA_disableChannel(DMA, DMA_CH2_CHAN_ID);

    // 停止采样定时器
    DL_TimerA_stopCounter(TIMER_A1_INST);

}

/**
 * @brief 强制触发
 */
void Oscilloscope_ForceTrigger(void)
{
    osc_config.trigger_found = true;
    osc_config.trigger_index = osc_config.buffer_index;
}

/**
 * @brief 单次触发
 */
void Oscilloscope_SingleTrigger(void)
{
    // 设置单次触发模式
    osc_config.trigger.mode = OSC_TRIGGER_SINGLE;
    osc_config.state = OSC_STATE_SINGLE;
    osc_config.buffer_index = 0;
    osc_config.data_ready = false;
    osc_config.trigger_found = false;

    // 启动ADC和DMA
    DL_ADC12_startConversion(USER_ADC_OSCILLOSCOPE_INST);
    DL_DMA_enableChannel(DMA, DMA_CH2_CHAN_ID);
    DL_TimerA_startCounter(TIMER_A1_INST);
}

/**
 * @brief 清除波形数据
 */
void Oscilloscope_ClearWaveform(void)
{
    memset(osc_config.ch1_buffer, 0, sizeof(osc_config.ch1_buffer));
    memset(osc_config.ch2_buffer, 0, sizeof(osc_config.ch2_buffer));
    memset(osc_config.display_buffer, 0, sizeof(osc_config.display_buffer));
    
    osc_config.buffer_index = 0;
    osc_config.sample_count = 0;
    osc_config.data_ready = false;
    osc_config.trigger_found = false;
}

/**
 * @brief 设置时基
 */
void Oscilloscope_SetTimebase(float time_per_div)
{
    osc_config.timebase.time_per_div = time_per_div;
    
    // 根据时基计算采样率
    // 假设显示10格，每格需要80个采样点
    float total_time = time_per_div * 10.0f;
    osc_config.timebase.sample_rate = (uint32_t)(800.0f / total_time);
    
    // 限制采样率范围
    if (osc_config.timebase.sample_rate > 100000) {
        osc_config.timebase.sample_rate = 100000;
    } else if (osc_config.timebase.sample_rate < 1000) {
        osc_config.timebase.sample_rate = 1000;
    }
}

/**
 * @brief 设置通道1量程
 */
void Oscilloscope_SetCH1Scale(float voltage_scale)
{
    osc_config.ch1.voltage_scale = voltage_scale;
}

/**
 * @brief 设置通道2量程
 */
void Oscilloscope_SetCH2Scale(float voltage_scale)
{
    osc_config.ch2.voltage_scale = voltage_scale;
}

/**
 * @brief 设置触发配置
 */
void Oscilloscope_SetTrigger(OscTriggerMode mode, OscTriggerEdge edge, uint16_t level)
{
    osc_config.trigger.mode = mode;
    osc_config.trigger.edge = edge;
    osc_config.trigger.level = level;
}

/**
 * @brief ADC数据处理回调函数
 */
void Oscilloscope_DataCallback(uint16_t ch1_data, uint16_t ch2_data)
{
    if (osc_config.state == OSC_STATE_STOP) {
        return;
    }

    // 存储数据到缓冲区
    if (osc_config.buffer_index < OSC_BUFFER_SIZE) {
        osc_config.ch1_buffer[osc_config.buffer_index] = ch1_data;
        osc_config.ch2_buffer[osc_config.buffer_index] = ch2_data;
        osc_config.buffer_index++;
        osc_config.sample_count++;
    }

    // 缓冲区满时标记数据就绪
    if (osc_config.buffer_index >= OSC_BUFFER_SIZE) {
        osc_config.data_ready = true;
        osc_config.buffer_index = 0;  // 循环缓冲
    }
}

/**
 * @brief 触发检测函数
 */
bool Oscilloscope_DetectTrigger(void)
{
    if (osc_config.buffer_index < 2) {
        return false;  // 数据不足
    }

    uint16_t current_sample, previous_sample;
    uint16_t trigger_level = osc_config.trigger.level;

    // 选择触发源
    if (osc_config.trigger.source == 0) {
        current_sample = osc_config.ch1_buffer[osc_config.buffer_index - 1];
        previous_sample = osc_config.ch1_buffer[osc_config.buffer_index - 2];
    } else {
        current_sample = osc_config.ch2_buffer[osc_config.buffer_index - 1];
        previous_sample = osc_config.ch2_buffer[osc_config.buffer_index - 2];
    }

    // 检测触发边沿
    bool trigger_detected = false;
    if (osc_config.trigger.edge == OSC_TRIGGER_RISING) {
        // 上升沿触发
        if (previous_sample < trigger_level && current_sample >= trigger_level) {
            trigger_detected = true;
        }
    } else {
        // 下降沿触发
        if (previous_sample > trigger_level && current_sample <= trigger_level) {
            trigger_detected = true;
        }
    }

    if (trigger_detected) {
        osc_config.trigger_index = osc_config.buffer_index - 1;
        osc_config.trigger_found = true;
    }

    return trigger_detected;
}

/**
 * @brief 波形测量函数
 */
void Oscilloscope_MeasureWaveform(void)
{
    if (!osc_config.data_ready) {
        osc_config.measurement.valid = false;
        Oscilloscope_ReadRealADC();
        return;
    }

    uint16_t *data = osc_config.ch1_buffer;  // 测量CH1
    uint16_t min_val = 4095, max_val = 0;
    uint32_t sum = 0;
    uint16_t zero_crossings = 0;
    uint16_t last_sample = data[0];

    // 计算基本统计量
    for (int i = 0; i < OSC_BUFFER_SIZE; i++) {
        uint16_t sample = data[i];

        if (sample < min_val) min_val = sample;
        if (sample > max_val) max_val = sample;
        sum += sample;

        // 检测过零点（用于频率测量）
        if (i > 0) {
            uint16_t mid_level = (min_val + max_val) / 2;
            if ((last_sample < mid_level && sample >= mid_level) ||
                (last_sample > mid_level && sample <= mid_level)) {
                zero_crossings++;
            }
        }
        last_sample = sample;
    }

    // 转换为电压值
    osc_config.measurement.max_value = Oscilloscope_ADCToVoltage(max_val, 0);
    osc_config.measurement.min_value = Oscilloscope_ADCToVoltage(min_val, 0);
    osc_config.measurement.mean = Oscilloscope_ADCToVoltage(sum / OSC_BUFFER_SIZE, 0);
    // 计算峰峰值
    osc_config.measurement.peak_to_peak = osc_config.measurement.max_value -
                                         osc_config.measurement.min_value;

    // 计算频率
    if (zero_crossings > 2) {
        float sample_time = (float)OSC_BUFFER_SIZE / osc_config.timebase.sample_rate;
        osc_config.measurement.frequency = (zero_crossings / 2.0f) / sample_time;
        osc_config.measurement.period = 1.0f / osc_config.measurement.frequency;
    } else {
        osc_config.measurement.frequency = 0.0f;
        osc_config.measurement.period = 0.0f;
    }

    // 计算RMS值（简化计算）
    uint64_t sum_squares = 0;
    uint32_t dc_offset = sum / OSC_BUFFER_SIZE;
    for (int i = 0; i < OSC_BUFFER_SIZE; i++) {
        int32_t ac_sample = (int32_t)data[i] - dc_offset;
        sum_squares += ac_sample * ac_sample;
    }
    float rms_adc = sqrtf((float)sum_squares / OSC_BUFFER_SIZE);
    osc_config.measurement.rms = Oscilloscope_ADCToVoltage((uint16_t)rms_adc + dc_offset, 0);

    osc_config.measurement.valid = true;
}

/**
 * @brief ADC值转换为电压值
 */
float Oscilloscope_ADCToVoltage(uint16_t adc_value, uint8_t channel)
{
    // ADC参考电压3.3V，12位分辨率
    float voltage = (float)adc_value * 3.3f / 4095.0f;

    // 应用通道量程和偏移
    if (channel == 0) {
        voltage = voltage * osc_config.ch1.voltage_scale + osc_config.ch1.offset;
    } else {
        voltage = voltage * osc_config.ch2.voltage_scale + osc_config.ch2.offset;
    }

    return voltage;
}

/**
 * @brief DMA传输完成中断处理函数
 * 需要在中断服务程序中调用
 */
void Oscilloscope_DMAComplete(void)
{
    // 标记数据就绪
    osc_config.data_ready = true;

    // 添加调试信息（临时启用）
    sprintf(uart_buffer, "[OSC_DMA] 传输完成！中断次数: %d\r\n", osc_dma_interrupt_count);
    UART_SendString(uart_buffer);

    // 显示前几个采样数据
    sprintf(uart_buffer, "[OSC_DMA] 前5个数据: %d, %d, %d, %d, %d\r\n",
            osc_config.ch1_buffer[0], osc_config.ch1_buffer[1], osc_config.ch1_buffer[2],
            osc_config.ch1_buffer[3], osc_config.ch1_buffer[4]);
    UART_SendString(uart_buffer);

    // 重新配置DMA进行下一次传输
    DL_DMA_setDestAddr(DMA, DMA_CH2_CHAN_ID, (uint32_t)osc_config.ch1_buffer);
    DL_DMA_setTransferSize(DMA, DMA_CH2_CHAN_ID, OSC_BUFFER_SIZE);
    DL_DMA_enableChannel(DMA, DMA_CH2_CHAN_ID);
}

/**
 * @brief 尝试读取真实ADC数据
 */
void Oscilloscope_ReadRealADC(void)
{
    // 填充缓冲区 - 逐个采样真实ADC数据
    for (int i = 0; i < OSC_BUFFER_SIZE; i++) {
        // 启动一次ADC转换
        DL_ADC12_startConversion(USER_ADC_OSCILLOSCOPE_INST);

        // 读取结果
        uint16_t adc_value = DL_ADC12_getMemResult(USER_ADC_OSCILLOSCOPE_INST, USER_ADC_OSCILLOSCOPE_ADCMEM_0);
        osc_config.ch1_buffer[i] = adc_value;
    }

    // 设置数据就绪
    osc_config.data_ready = true;
    osc_config.buffer_index = OSC_BUFFER_SIZE;
}
