/**
 * @file state_machine.c
 * @brief 状态机处理模块实现
 * @version 1.0
 * @date 2025-07-17
 * 
 * 本文件实现了状态机的核心逻辑，包括页面切换、命令分发和处理
 */

#include "state_machine.h"
#include "tjc.h"
#include "usart.h"
#include "motor.h"
#include "send_info.h"
#include "oscilloscope.h"
#include "app_types.h"
#include <stdio.h>
#include <string.h>

extern bool motor_init;

/* 状态机状态实例 */
StateMachine_State sm_state = {
    .current_page_id = PAGE_MAIN,
    .new_command_received = false,
    .received_frame = {0}
};

/* 示波器初始化状态 */
static bool oscilloscope_initialized = false;

/* 外部变量引用 */
extern char uart_buffer[1000];
extern int motor_speed;

/**
 * @brief 初始化状态机
 */
void StateMachine_Init(void)
{
    sm_state.current_page_id = PAGE_MAIN;
    sm_state.new_command_received = false;
    memset(&sm_state.received_frame, 0, sizeof(TJC_Frame));
    oscilloscope_initialized = false;

    // 初始化按键标志位
    sm_state.key_pressed = false;
    sm_state.key_long_pressed = false;
    sm_state.key_released = false;

    // 示波器模块将在首次进入示波器页面时初始化

}

/**
 * @brief 主状态机处理函数
 */
void StateMachine_Process(void)
{
    // 处理按键事件
    StateMachine_HandleKeyEvents();

    StateMachine_HandleDataSending();

    // 检查是否有新命令需要处理
    if (!sm_state.new_command_received) {
        return;
    }

    // 清除新命令标志 - 防止重复处理
    sm_state.new_command_received = false;

    // 根据当前页面ID进入对应的状态处理
    switch (sm_state.current_page_id) {
        case PAGE_MAIN:
            StateMachine_HandleMainPage();
            break;
            
        case PAGE_MOTOR:
            StateMachine_HandleMotorPage();
            break;
            
        case PAGE_OSCILLOSCOPE:
            StateMachine_HandleOscilloscopePage();
            break;
            
        default:
            // 默认按主页面处理
            StateMachine_HandleMainPage();
            break;
    }

    // 各个界面数据发送处理
    StateMachine_HandleDataSending();

}

/**
 * @brief TJC协议帧接收回调函数
 */
void StateMachine_FrameReceivedCallback(const TJC_Frame* frame)
{
    // 复制接收到的帧数据
    memcpy(&sm_state.received_frame, frame, sizeof(TJC_Frame));

    // 更新当前页面ID
    sm_state.current_page_id = frame->page_id;

    // 设置新命令接收标志
    sm_state.new_command_received = true;

    // 发送接收确认
    sprintf(uart_buffer, "解析到的帧: Page=%d, Cmd=%d, Len=%d\r\n",
            frame->page_id, frame->command, frame->length);
    UART_SendString(uart_buffer);
}

/**
 * @brief 设置当前页面ID
 */
void StateMachine_SetCurrentPage(uint8_t page_id)
{
    sm_state.current_page_id = page_id;
}

/**
 * @brief 获取当前页面ID
 */
uint8_t StateMachine_GetCurrentPage(void)
{
    return sm_state.current_page_id;
}

/**
 * @brief 检查是否有新命令需要处理
 */
bool StateMachine_HasNewCommand(void)
{
    return sm_state.new_command_received;
}

/**
 * @brief 主界面命令处理 - 只处理界面跳转
 */
void StateMachine_HandleMainPage(void)
{
    switch (sm_state.received_frame.command) {
        case CMD_NAVIGATION:
            if (sm_state.received_frame.length >= 1) {
                uint8_t nav_cmd = sm_state.received_frame.data[0];
                StateMachine_HandleNavigation(nav_cmd);
            } else {

            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief 电机控制界面命令处理
 */
void StateMachine_HandleMotorPage(void)
{
    switch (sm_state.received_frame.command) {
        case CMD_MOTOR_CONTROL:
            if (sm_state.received_frame.length >= 1) {
                uint8_t motor_cmd = sm_state.received_frame.data[0];

                StateMachine_HandleMotorControl(motor_cmd);
            } else {

            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief 示波器界面命令处理
 */
void StateMachine_HandleOscilloscopePage(void)
{
    switch (sm_state.received_frame.command) {
        case CMD_OSCILLOSCOPE:
            if (sm_state.received_frame.length >= 1) {
                uint8_t osc_cmd = sm_state.received_frame.data[0];
                uint8_t osc_value = (sm_state.received_frame.length >= 2) ? sm_state.received_frame.data[1] : 0;
                StateMachine_HandleOscilloscopeControl(osc_cmd, osc_value);
            } else {

            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief 界面跳转命令处理
 */
void StateMachine_HandleNavigation(uint8_t nav_cmd)
{

    switch (nav_cmd) {
        case NAV_TO_MAIN:
            StateMachine_SetCurrentPage(PAGE_MAIN);
            break;

        case NAV_TO_MOTOR:
            StateMachine_SetCurrentPage(PAGE_MOTOR);
            break;

        case NAV_TO_OSCILLOSCOPE:
            StateMachine_SetCurrentPage(PAGE_OSCILLOSCOPE);
            break;

        default:
            break;
    }
}

/**
 * @brief 电机控制命令处理
 */
void StateMachine_HandleMotorControl(uint8_t motor_cmd)
{
    if (!motor_init) {
        motor_init = true;
        MOTOR_Init(); // 使用MOTOER_Init函数初始化定时器8的通道0（PA_12）和1（PA_13）
    }
    

    switch (motor_cmd) {
        case MOTOR_CMD_INCREASE_SPEED:
            // 增加速度
            {
                int new_speed = motor_speed + 10; // 每次增加10%
                if (new_speed > 100) new_speed = 100;
                set_motor_speed(new_speed);
            }
            break;

        case MOTOR_CMD_DECREASE_SPEED:
            // 减少速度
            {
                int new_speed = motor_speed - 10; // 每次减少10%
                if (new_speed < -100) new_speed = -100;
                set_motor_speed(new_speed);
            }
            break;

        case MOTOR_CMD_START:
            // 启动电机 - 使用默认50%速度
            set_motor_speed(50);
            break;

        case MOTOR_CMD_STOP:
            // 停止电机
            set_motor_speed(0);
            break;

        case MOTOR_CMD_REVERSE:
            // 反向运动
            {
                int reverse_speed = -motor_speed;
                if (reverse_speed == 0) {
                    reverse_speed = -50; // 默认反向50%
                }
                set_motor_speed(reverse_speed);
            }
            break;

        default:
            break;
    }
}

/**
 * @brief 示波器控制命令处理
 */
void StateMachine_HandleOscilloscopeControl(uint8_t osc_cmd, uint8_t osc_value)
{
    // 确保示波器已初始化
    if (!oscilloscope_initialized) {
        Oscilloscope_Init();
        oscilloscope_initialized = true;
    }

    switch (osc_cmd) {
        case OSC_CMD_RUN_STOP:
            // RUN/STOP命令：根据当前状态切换运行/停止
            if (osc_config.state == OSC_STATE_RUN) {
                // 当前运行中，执行停止
                Oscilloscope_Stop();
                sprintf(uart_buffer, "示波器已停止\r\n");
                UART_SendString(uart_buffer);
            } else {
                // 当前停止中，执行启动
                Oscilloscope_Start();
                sprintf(uart_buffer, "示波器已启动\r\n");
                UART_SendString(uart_buffer);
            }
            break;

        case OSC_CMD_SINGLE:
            // SINGLE命令：单次触发
            Oscilloscope_SingleTrigger();
            sprintf(uart_buffer, "执行单次触发\r\n");
            UART_SendString(uart_buffer);
            break;

        case OSC_CMD_AUTO:
            // AUTO命令：自动触发模式
            Oscilloscope_SetTrigger(OSC_TRIGGER_AUTO, OSC_TRIGGER_RISING, 2048);
            sprintf(uart_buffer, "设置自动触发模式\r\n");
            UART_SendString(uart_buffer);
            break;

        case OSC_CMD_FORCE:
            // FORCE命令：强制触发
            Oscilloscope_ForceTrigger();
            // 确保有数据可用
            if (!osc_config.data_ready) {
                Oscilloscope_ReadRealADC();
            }
            // 执行测量
            Oscilloscope_MeasureWaveform();
            sprintf(uart_buffer, "执行强制触发\r\n");
            UART_SendString(uart_buffer);
            break;

        case OSC_CMD_CLEAR:
            // CLEAR命令：清除波形数据
            Oscilloscope_ClearWaveform();
            sprintf(uart_buffer, "波形数据已清除\r\n");
            UART_SendString(uart_buffer);
            break;

        case OSC_CMD_MEASURE:
            // MEASURE命令：执行测量并发送结果
            if (!osc_config.data_ready) {
                Oscilloscope_ReadRealADC();
            }
            Oscilloscope_MeasureWaveform();
            Oscilloscope_SendMeasurement();
            sprintf(uart_buffer, "测量完成\r\n");
            UART_SendString(uart_buffer);
            break;

        default:
            sprintf(uart_buffer, "未知示波器命令: 0x%02X\r\n", osc_cmd);
            UART_SendString(uart_buffer);
            break;
    }
}

/**
 * @brief 各个界面数据发送处理
 */
void StateMachine_HandleDataSending(void)
{
    static uint32_t last_status_time = 0;      // 上次发送状态的时间
    static uint32_t last_serial_time = 0;      // 上次发送串口数据的时间

    uint32_t current_time = get_tick_ms();

    switch (sm_state.current_page_id) {
        case PAGE_MOTOR:

            // Send_Debug_Info_Port() 每100ms发送一次
            if (current_time - last_status_time >= 100) {
                Send_Debug_Info_Port();
                last_status_time = current_time;
            }
            
            break;
        case PAGE_OSCILLOSCOPE:
            // 示波器运行时才发送页面数据
            if (osc_config.state == OSC_STATE_RUN) {
                // 保证在运行状态下持续测量和发送数据
                Oscilloscope_ReadRealADC();
                Oscilloscope_MeasureWaveform();

                // Oscilloscope_SendStatus() 每300ms发送一次
                if (current_time - last_status_time >= 300) {
                    Oscilloscope_SendStatus();
                    last_status_time = current_time;
                }

                // Oscilloscope_SendWaveformData() 每200ms发送一次
                if (current_time - last_serial_time >= 200) {
                    Oscilloscope_SendWaveformData();
                    last_serial_time = current_time;
                }
            } else {
                // 停止状态下只发送状态信息，频率较低
                if (current_time - last_status_time >= 800) {
                    Oscilloscope_SendStatus();
                    last_status_time = current_time;
                }
            }
            break;
        default:
            // 其他页面不需要特殊数据发送
            break;
    }
}

/**
 * @brief 处理按键事件
 */
void StateMachine_HandleKeyEvents(void)
{

    // sm_state.current_page_id = PAGE_MOTOR;

    // 处理按键按下事件 - 专用于电机控制
    if (sm_state.key_pressed) {
        sm_state.key_pressed = false;  // 清除标志

        // 按键功能：电机加速（只在电机控制页面有效）
        if (sm_state.current_page_id == PAGE_MOTOR) {
            // 电机加速
            StateMachine_HandleMotorSpeedIncrease();
        } else {

        }
    }

    // 处理长按事件 - 专用于电机控制
    if (sm_state.key_long_pressed) {
        sm_state.key_long_pressed = false;  // 清除标志

        // 长按功能：电机暂停/停止（只在电机控制页面有效）
        if (sm_state.current_page_id == PAGE_MOTOR) {
            // 电机暂停/停止
            StateMachine_HandleMotorStop();
        } else {

        }
    }

    // 处理按键释放事件
    if (sm_state.key_released) {
        sm_state.key_released = false;  // 清除标志
        // 按键释放时的处理（如停止LED闪烁等）
    }

    // sm_state.current_page_id = PAGE_MAIN;
}

/**
 * @brief 设置按键按下标志
 */
void StateMachine_SetKeyPressed(void)
{
    sm_state.key_pressed = true;
}

/**
 * @brief 设置按键长按标志
 */
void StateMachine_SetKeyLongPressed(void)
{
    sm_state.key_long_pressed = true;
}

/**
 * @brief 设置按键释放标志
 */
void StateMachine_SetKeyReleased(void)
{
    sm_state.key_released = true;
}

/**
 * @brief 处理电机加速
 */
void StateMachine_HandleMotorSpeedIncrease(void)
{
    // 获取当前电机速度
    static int motor_speed = 0;

    // 增加电机速度（限制最大值）
    if (motor_speed < 100) {
        motor_speed += 10;  // 每次增加10%

        // 设置电机速度
        set_motor_speed(motor_speed);

    } else {

    }
}

/**
 * @brief 处理电机停止
 */
void StateMachine_HandleMotorStop(void)
{
    // 停止电机
    set_motor_speed(0);

    // 重置速度变量
    static int motor_speed = 0;
    motor_speed = 0;

}