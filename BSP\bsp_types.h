/*
 * bsp_types.h
 * 基本类型和定义，用于避免包含链接错误
 */
#ifndef BSP_TYPES_H_
#define BSP_TYPES_H_

/* 基本数据类型定义 */
typedef signed char         int8_t;
typedef unsigned char       uint8_t;
typedef signed short        int16_t;
typedef unsigned short      uint16_t;
typedef signed int          int32_t;
typedef unsigned int        uint32_t;

/* 引脚定义 */
#ifndef PB6
#define PB6 (1 << 6)  /* GPIOB Pin 6 */
#endif

#ifndef PB16
#define PB16 (1 << 16)  /* GPIOB Pin 16 */
#endif

/* QEI相关定义 */
#define QEI_DIR_FWD 1  // 正向旋转
#define QEI_DIR_REV 0  // 反向旋转

#endif /* BSP_TYPES_H_ */ 